<?php include 'app/views/shares/header.php'; ?>
<div class="container py-5">
    <h1 class="mb-4 text-primary">Thêm danh mục</h1>

    <?php if (!empty($errors)): ?>
        <div class="alert alert-danger">
            <ul>
                <?php foreach ($errors as $field => $error): ?>
                    <li><?php echo htmlspecialchars($error, ENT_QUOTES, 'UTF-8'); ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>

    <form method="POST" action="/lab01/category/save" class="shadow p-4 bg-light rounded">
        <div class="form-group mb-3">
            <label for="name" class="form-label">Tên danh mục:</label>
            <input type="text" id="name" name="name" class="form-control" value="<?php echo htmlspecialchars($_POST['name'] ?? '', ENT_QUOTES, 'UTF-8'); ?>" required>
        </div>
        <div class="form-group mb-3">
            <label for="description" class="form-label"><PERSON><PERSON> tả:</label>
            <textarea id="description" name="description" class="form-control" rows="4" required><?php echo htmlspecialchars($_POST['description'] ?? '', ENT_QUOTES, 'UTF-8'); ?></textarea>
        </div>
        <button type="submit" class="btn btn-success">Thêm danh mục</button>
        <a href="/lab01/category/list" class="btn btn-secondary">Quay lại</a>
    </form>
</div>
<?php include 'app/views/shares/footer.php'; ?>
