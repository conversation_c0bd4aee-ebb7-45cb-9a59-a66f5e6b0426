<?php include 'app/views/shares/header.php'; ?>

<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-warning text-dark">
                    <h3 class="mb-0"><i class="fas fa-edit"></i> Sửa đơn hàng #<?php echo $order->id; ?></h3>
                </div>
                <div class="card-body">
                    <?php if (!empty($errors)): ?>
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                <?php foreach ($errors as $error): ?>
                                    <li><?php echo htmlspecialchars($error, ENT_QUOTES, 'UTF-8'); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <form action="/lab01/account/editOrder/<?php echo $order->id; ?>" method="POST">
                        <div class="form-group mb-3">
                            <label for="user_id" class="form-label">T<PERSON><PERSON> k<PERSON> kh<PERSON>ch hàng</label>
                            <select id="user_id" name="user_id" class="form-control">
                                <option value="">-- Khách vãng lai --</option>
                                <?php foreach ($accounts as $account): ?>
                                    <option value="<?php echo $account->id; ?>" 
                                            <?php echo ($order->user_id == $account->id) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($account->username . ' - ' . $account->fullname, ENT_QUOTES, 'UTF-8'); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="form-group mb-3">
                            <label for="name" class="form-label">Tên người nhận <span class="text-danger">*</span></label>
                            <input type="text" id="name" name="name" class="form-control" 
                                   value="<?php echo htmlspecialchars($order->name, ENT_QUOTES, 'UTF-8'); ?>" 
                                   required>
                        </div>

                        <div class="form-group mb-3">
                            <label for="phone" class="form-label">Số điện thoại <span class="text-danger">*</span></label>
                            <input type="tel" id="phone" name="phone" class="form-control" 
                                   value="<?php echo htmlspecialchars($order->phone, ENT_QUOTES, 'UTF-8'); ?>" 
                                   required>
                        </div>

                        <div class="form-group mb-3">
                            <label for="address" class="form-label">Địa chỉ giao hàng <span class="text-danger">*</span></label>
                            <textarea id="address" name="address" class="form-control" rows="3" required><?php echo htmlspecialchars($order->address, ENT_QUOTES, 'UTF-8'); ?></textarea>
                        </div>

                        <div class="form-group mb-3">
                            <label for="status" class="form-label">Trạng thái đơn hàng</label>
                            <select id="status" name="status" class="form-control">
                                <option value="pending" <?php echo ($order->status == 'pending') ? 'selected' : ''; ?>>Chờ xử lý</option>
                                <option value="processing" <?php echo ($order->status == 'processing') ? 'selected' : ''; ?>>Đang xử lý</option>
                                <option value="completed" <?php echo ($order->status == 'completed') ? 'selected' : ''; ?>>Hoàn thành</option>
                                <option value="cancelled" <?php echo ($order->status == 'cancelled') ? 'selected' : ''; ?>>Đã hủy</option>
                            </select>
                        </div>

                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle"></i> Thông tin đơn hàng</h6>
                            <p class="mb-1"><strong>Ngày tạo:</strong> <?php echo date('d/m/Y H:i', strtotime($order->order_date)); ?></p>
                            <?php if ($order->username): ?>
                                <p class="mb-0"><strong>Tài khoản:</strong> <?php echo htmlspecialchars($order->username . ' - ' . $order->fullname, ENT_QUOTES, 'UTF-8'); ?></p>
                            <?php endif; ?>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-save"></i> Cập nhật đơn hàng
                            </button>
                            <a href="/lab01/account/orderDetail/<?php echo $order->id; ?>" class="btn btn-info">
                                <i class="fas fa-eye"></i> Xem chi tiết
                            </a>
                            <a href="/lab01/account/orderManagement" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Quay lại
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener("DOMContentLoaded", function() {
    // Validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(event) {
        const name = document.getElementById('name').value.trim();
        const phone = document.getElementById('phone').value.trim();
        const address = document.getElementById('address').value.trim();
        
        if (!name) {
            alert('Vui lòng nhập tên người nhận!');
            event.preventDefault();
            return;
        }
        
        if (!phone) {
            alert('Vui lòng nhập số điện thoại!');
            event.preventDefault();
            return;
        }
        
        if (!address) {
            alert('Vui lòng nhập địa chỉ giao hàng!');
            event.preventDefault();
            return;
        }
    });
});
</script>

<?php include 'app/views/shares/footer.php'; ?>
