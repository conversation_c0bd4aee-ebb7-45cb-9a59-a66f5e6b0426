<?php include 'app/views/shares/header.php'; ?>

<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-success text-white">
                    <h3 class="mb-0"><i class="fas fa-plus"></i> Thêm đơn hàng mới</h3>
                </div>
                <div class="card-body">
                    <?php if (!empty($errors)): ?>
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                <?php foreach ($errors as $error): ?>
                                    <li><?php echo htmlspecialchars($error, ENT_QUOTES, 'UTF-8'); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <form action="/lab01/account/addOrder" method="POST">
                        <div class="form-group mb-3">
                            <label for="user_id" class="form-label">T<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> khách hàng</label>
                            <select id="user_id" name="user_id" class="form-control">
                                <option value="">-- Khách vãng lai --</option>
                                <?php foreach ($accounts as $account): ?>
                                    <option value="<?php echo $account->id; ?>" 
                                            <?php echo (isset($_POST['user_id']) && $_POST['user_id'] == $account->id) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($account->username . ' - ' . $account->fullname, ENT_QUOTES, 'UTF-8'); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="form-group mb-3">
                            <label for="name" class="form-label">Tên người nhận <span class="text-danger">*</span></label>
                            <input type="text" id="name" name="name" class="form-control" 
                                   value="<?php echo isset($_POST['name']) ? htmlspecialchars($_POST['name'], ENT_QUOTES, 'UTF-8') : ''; ?>" 
                                   required>
                        </div>

                        <div class="form-group mb-3">
                            <label for="phone" class="form-label">Số điện thoại <span class="text-danger">*</span></label>
                            <input type="tel" id="phone" name="phone" class="form-control" 
                                   value="<?php echo isset($_POST['phone']) ? htmlspecialchars($_POST['phone'], ENT_QUOTES, 'UTF-8') : ''; ?>" 
                                   required>
                        </div>

                        <div class="form-group mb-3">
                            <label for="address" class="form-label">Địa chỉ giao hàng <span class="text-danger">*</span></label>
                            <textarea id="address" name="address" class="form-control" rows="3" required><?php echo isset($_POST['address']) ? htmlspecialchars($_POST['address'], ENT_QUOTES, 'UTF-8') : ''; ?></textarea>
                        </div>

                        <div class="form-group mb-3">
                            <label for="status" class="form-label">Trạng thái đơn hàng</label>
                            <select id="status" name="status" class="form-control">
                                <option value="pending" <?php echo (isset($_POST['status']) && $_POST['status'] == 'pending') ? 'selected' : ''; ?>>Chờ xử lý</option>
                                <option value="processing" <?php echo (isset($_POST['status']) && $_POST['status'] == 'processing') ? 'selected' : ''; ?>>Đang xử lý</option>
                                <option value="completed" <?php echo (isset($_POST['status']) && $_POST['status'] == 'completed') ? 'selected' : ''; ?>>Hoàn thành</option>
                                <option value="cancelled" <?php echo (isset($_POST['status']) && $_POST['status'] == 'cancelled') ? 'selected' : ''; ?>>Đã hủy</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save"></i> Thêm đơn hàng
                            </button>
                            <a href="/lab01/account/orderManagement" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Quay lại
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener("DOMContentLoaded", function() {
    // Auto-fill thông tin khi chọn tài khoản
    const userSelect = document.getElementById('user_id');
    const nameInput = document.getElementById('name');
    const phoneInput = document.getElementById('phone');
    
    userSelect.addEventListener('change', function() {
        if (this.value) {
            // Có thể thêm AJAX call để lấy thông tin chi tiết của user
            // Hiện tại chỉ clear form khi chọn khách vãng lai
        } else {
            // Clear form khi chọn khách vãng lai
            nameInput.value = '';
            phoneInput.value = '';
        }
    });
    
    // Validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(event) {
        const name = nameInput.value.trim();
        const phone = phoneInput.value.trim();
        const address = document.getElementById('address').value.trim();
        
        if (!name) {
            alert('Vui lòng nhập tên người nhận!');
            event.preventDefault();
            return;
        }
        
        if (!phone) {
            alert('Vui lòng nhập số điện thoại!');
            event.preventDefault();
            return;
        }
        
        if (!address) {
            alert('Vui lòng nhập địa chỉ giao hàng!');
            event.preventDefault();
            return;
        }
    });
});
</script>

<?php include 'app/views/shares/footer.php'; ?>
