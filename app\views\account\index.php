<?php include 'app/views/shares/header.php'; ?>
<div class="container py-5">
    <h2 class="mb-4">Quản lý tà<PERSON>n</h2>
    <a href="/lab01/account/register" class="btn btn-success mb-3">Thêm tài khoản mới</a>
    <table class="table table-bordered table-hover">
        <thead class="thead-dark">
            <tr>
                <th>ID</th>
                <th>Tên đăng nhập</th>
                <th>Họ tên</th>
                <th>Vai trò</th>
                <th>Hành động</th>
            </tr>
        </thead>
        <tbody>
            <?php if (!empty($accounts)) : ?>
                <?php foreach ($accounts as $account) : ?>
                    <tr>
                        <td><?= htmlspecialchars($account->id) ?></td>
                        <td><?= htmlspecialchars($account->username) ?></td>
                        <td><?= htmlspecialchars($account->fullname) ?></td>
                        <td>
                            <?php if ($account->role === 'admin'): ?>
                                <span class="badge badge-danger">Admin</span>
                            <?php else: ?>
                                <span class="badge badge-secondary">User</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <a href="/lab01/account/accountDetail/<?= $account->id ?>" class="btn btn-info btn-sm">Chi tiết</a>
                            <a href="/lab01/account/editAccount/<?= $account->id ?>" class="btn btn-primary btn-sm">Sửa</a>
                            <?php if ($account->username !== $_SESSION['username']): ?>
                                <a href="/lab01/account/deleteAccount/<?= $account->id ?>" class="btn btn-danger btn-sm"
                                   onclick="return confirm('Bạn có chắc chắn muốn xóa?');">Xóa</a>
                            <?php endif; ?>
                        </td>
                    </tr>
                <?php endforeach; ?>
            <?php else : ?>
                <tr>
                    <td colspan="5" class="text-center">Không có tài khoản nào.</td>
                </tr>
            <?php endif; ?>
        </tbody>
    </table>
</div>
<?php include 'app/views/shares/footer.php'; ?>