<?php include 'app/views/shares/header.php'; ?>

<div class="container py-4">
    <div class="row">
        <div class="col-12">
            <h1 class="text-primary mb-4">
                <i class="fas fa-shopping-cart"></i> Giỏ hàng của bạn
            </h1>

            <?php if (!empty($cart)): ?>
                <div class="row">
                    <div class="col-lg-8">
                        <div class="card shadow-sm">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">Sản phẩm trong giỏ hàng</h5>
                            </div>
                            <div class="card-body p-0">
                                <?php
                                $totalAmount = 0;
                                foreach ($cart as $id => $item):
                                    $itemTotal = $item['price'] * $item['quantity'];
                                    $totalAmount += $itemTotal;
                                ?>
                                    <div class="cart-item border-bottom p-3">
                                        <div class="row align-items-center">
                                            <div class="col-md-2">
                                                <div class="product-image">
                                                    <?php if ($item['image']): ?>
                                                        <img src="/lab01/<?php echo htmlspecialchars($item['image'], ENT_QUOTES, 'UTF-8'); ?>"
                                                             alt="<?php echo htmlspecialchars($item['name'], ENT_QUOTES, 'UTF-8'); ?>"
                                                             class="img-fluid rounded"
                                                             style="width: 80px; height: 80px; object-fit: cover;">
                                                    <?php else: ?>
                                                        <div class="bg-light rounded d-flex align-items-center justify-content-center"
                                                             style="width: 80px; height: 80px;">
                                                            <i class="fas fa-image text-muted"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <h6 class="mb-1"><?php echo htmlspecialchars($item['name'], ENT_QUOTES, 'UTF-8'); ?></h6>
                                                <p class="text-muted mb-0">
                                                    <small>Đơn giá: <?php echo number_format($item['price'], 0, ',', '.'); ?> VND</small>
                                                </p>
                                            </div>
                                            <div class="col-md-3">
                                                <form method="POST" action="/lab01/product/updateCart" class="d-flex align-items-center justify-content-center">
                                                    <input type="hidden" name="product_id" value="<?php echo $id; ?>">
                                                    <button type="submit" name="action" value="decrease" class="btn btn-sm btn-outline-secondary">
                                                        <i class="fas fa-minus">-</i>
                                                    </button>
                                                    <input type="text" name="quantity"
                                                           value="<?php echo htmlspecialchars($item['quantity'], ENT_QUOTES, 'UTF-8'); ?>"
                                                           class="form-control text-center mx-2"
                                                           style="width: 60px;" readonly>
                                                    <button type="submit" name="action" value="increase" class="btn btn-sm btn-outline-secondary">
                                                        <i class="fas fa-plus">+</i>
                                                    </button>
                                                </form>
                                            </div>
                                            <div class="col-md-3 text-end">
                                                <h6 class="text-primary mb-0">
                                                    <?php echo number_format($itemTotal, 0, ',', '.'); ?> VND
                                                </h6>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4">
                        <div class="card shadow-sm">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0">Tổng cộng</h5>
                            </div>
                            <div class="card-body">
                                <div class="d-flex justify-content-between mb-3">
                                    <span>Tổng số sản phẩm:</span>
                                    <span class="fw-bold"><?php echo array_sum(array_column($cart, 'quantity')); ?></span>
                                </div>
                                <div class="d-flex justify-content-between mb-3">
                                    <span>Tổng tiền:</span>
                                    <span class="h5 text-success mb-0"><?php echo number_format($totalAmount, 0, ',', '.'); ?> VND</span>
                                </div>
                                <hr>
                                <div class="d-grid gap-2">
                                    <a href="/lab01/product/checkout" class="btn btn-success btn-lg">
                                        <i class="fas fa-credit-card"></i> Thanh toán
                                    </a>
                                    <a href="/lab01/product" class="btn btn-outline-primary">
                                        <i class="fas fa-shopping-bag"></i> Tiếp tục mua sắm
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <div class="empty-cart">
                        <i class="fas fa-shopping-cart fa-5x text-muted mb-3"></i>
                        <h4 class="text-muted">Giỏ hàng của bạn đang trống</h4>
                        <p class="text-muted">Hãy thêm một số sản phẩm vào giỏ hàng để tiếp tục mua sắm!</p>
                        <a href="/lab01/product" class="btn btn-primary btn-lg">
                            <i class="fas fa-shopping-bag"></i> Bắt đầu mua sắm
                        </a>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php include 'app/views/shares/footer.php'; ?>

<style>
.cart-item {
    transition: background-color 0.2s ease;
}

.cart-item:hover {
    background-color: #f8f9fa;
}

.product-image img {
    transition: transform 0.2s ease;
}

.product-image img:hover {
    transform: scale(1.05);
}

.empty-cart i {
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.btn {
    transition: all 0.2s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

@media (max-width: 768px) {
    .cart-item .row {
        text-align: center;
    }

    .cart-item .col-md-3:last-child {
        margin-top: 10px;
    }
}
</style>
