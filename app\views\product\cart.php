<?php include 'app/views/shares/header.php'; ?>
<div class="px-5">
<h1>Giỏ hàng</h1>
<?php if (!empty($cart)): ?>
    <ul class="list-group">
        <?php foreach ($cart as $id => $item): ?>
            <li class="list-group-item d-flex align-items-center justify-content-between">
                <div>
                    <h2><?php echo htmlspecialchars($item['name'], ENT_QUOTES, 'UTF-8'); ?></h2>
                    <?php if ($item['image']): ?>
                        <img src="/lab01/<?php echo $item['image']; ?>" alt="product Image" style="max-width: 100px;">
                    <?php endif; ?>
                    <p>Giá: <?php 
                        $totalPrice = $item['price'] * $item['quantity'];
                        echo number_format($totalPrice, 0, ',', '.') . " VND"; 
                    ?></p>
                </div>
                <div class="d-flex align-items-center">
                    <form method="POST" action="/lab01/product/updateCart" class="d-flex align-items-center">
                        <input type="hidden" name="product_id" value="<?php echo $id; ?>">
                        <button type="submit" name="action" value="decrease" class="btn btn-sm btn-outline-secondary">-</button>
                        <input type="text" name="quantity" value="<?php echo htmlspecialchars($item['quantity'], ENT_QUOTES, 'UTF-8'); ?>" size="2" class="text-center mx-1" style="width: 40px;" readonly>
                        <button type="submit" name="action" value="increase" class="btn btn-sm btn-outline-secondary">+</button>
                    </form>
                </div>
            </li>
        <?php endforeach; ?>
    </ul>
<?php else: ?>
    <p>Giỏ hàng của bạn đang trống.</p>
<?php endif; ?>
<a href="/lab01/product" class="btn btn-secondary mt-2">Tiếp tục mua sắm</a>
<a href="/lab01/product/checkout" class="btn btn-secondary mt-2">Thanh Toán</a>
</div>
<?php include 'app/views/shares/footer.php'; ?>
