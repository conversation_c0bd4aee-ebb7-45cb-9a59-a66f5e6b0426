
-- Dumping structure for table my_store.account
DROP TABLE IF EXISTS `account`;
CREATE TABLE IF NOT EXISTS `account` (
  `id` int NOT NULL AUTO_INCREMENT,
  `username` varchar(255) NOT NULL,
  `email` varchar(255) UNIQUE,
  `phone` varchar(20),
  `verification_email` varchar(255),
  `fullname` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `role` enum('admin','user') DEFAULT 'user',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Dumping data for table my_store.account: ~1 rows (approximately)
INSERT INTO `account` (`id`, `username`, `fullname`, `password`, `role`) VALUES
	(1, 'q', 'q', '$2y$10$Cj5yRHlC2gsXZayDdZKNeO2N0Wd0pzZ4nRGbz9zMMwy3LyydRxXpy', 'admin'),
	(2, 'u', 'u', '$2y$10$U3Kh58h.GcFUBzAIQVmpuuYbpH1Ny4a3u6aEixSIqrGh2n8d.LrbC', 'user');

-- Dumping structure for table my_store.category
DROP TABLE IF EXISTS `category`;
CREATE TABLE IF NOT EXISTS `category` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` text,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Dumping data for table my_store.category: ~4 rows (approximately)
INSERT INTO `category` (`id`, `name`, `description`) VALUES
	(1, 'Electronics', 'All kinds of electronics like phones, laptops, etc.'),
	(2, 'Clothing', 'Fashionable clothes for all ages and sizes'),
	(3, 'Home Appliances', 'Home appliances such as refrigerators, washing machines, etc.'),
	(4, 'Books', 'Books from various genres including fiction, non-fiction, and academic'),
	(5, 'test_new', 'fafafafa');

-- Dumping structure for table my_store.orders
DROP TABLE IF EXISTS `orders`;
CREATE TABLE IF NOT EXISTS `orders` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `address` text NOT NULL,
   `status` enum('pending','processing','completed','cancelled') DEFAULT 'pending' AFTER `address`;
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Dumping data for table my_store.orders: ~2 rows (approximately)
INSERT INTO `orders` (`id`, `name`, `phone`, `address`, `created_at`) VALUES
	(1, 'tuan anh', '012345678', 'quan 10', '2025-03-07 13:08:29'),
	(2, 'trong', '09876543', 'quan tân bình', '2025-03-07 13:09:57'),
	(3, 'u', '1234', 'df', '2025-03-25 06:25:02');

-- Dumping structure for table my_store.order_details
DROP TABLE IF EXISTS `order_details`;
CREATE TABLE IF NOT EXISTS `order_details` (
  `id` int NOT NULL AUTO_INCREMENT,
  `order_id` int NOT NULL,
  `product_id` int NOT NULL,
  `quantity` int NOT NULL,
  `price` decimal(10,2) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `order_id` (`order_id`),
  CONSTRAINT `order_details_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Dumping data for table my_store.order_details: ~2 rows (approximately)
INSERT INTO `order_details` (`id`, `order_id`, `product_id`, `quantity`, `price`) VALUES
	(1, 1, 17, 2, 12000.00),
	(2, 2, 14, 1, 140000.00),
	(3, 3, 15, 1, 17000.00);

-- Dumping structure for table my_store.product
DROP TABLE IF EXISTS `product`;
CREATE TABLE IF NOT EXISTS `product` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` text,
  `price` decimal(10,2) NOT NULL,
  `image` varchar(255) DEFAULT NULL,
  `category_id` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `category_id` (`category_id`),
  CONSTRAINT `product_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `category` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Dumping data for table my_store.product: ~4 rows (approximately)
INSERT INTO `product` (`id`, `name`, `description`, `price`, `image`, `category_id`) VALUES
	(16, 'dien thoai', 'iphone14', 13000.00, 'uploads/download.jpeg', 2),
	(18, 'api', 'api', 12.00, NULL, 1),
	(19, 'testapi', 'oiuytr', 1200.00, NULL, 1);

/*!40103 SET TIME_ZONE=IFNULL(@OLD_TIME_ZONE, 'system') */;
/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;
/*!40014 SET FOREIGN_KEY_CHECKS=IFNULL(@OLD_FOREIGN_KEY_CHECKS, 1) */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40111 SET SQL_NOTES=IFNULL(@OLD_SQL_NOTES, 1) */;

ALTER TABLE `account` ADD COLUMN `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP AFTER `role`;

-- Cập nhật bảng orders
ALTER TABLE `orders` ADD COLUMN `user_id` int NULL AFTER `id`;
ALTER TABLE `orders` ADD CONSTRAINT `orders_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `account` (`id`) ON DELETE SET NULL;

-- Thêm cột status và đổi tên created_at thành order_date
ALTER TABLE `orders` ADD COLUMN `status` enum('pending','processing','completed','cancelled') DEFAULT 'pending' AFTER `address`;
ALTER TABLE `orders` CHANGE COLUMN `created_at` `order_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP;
