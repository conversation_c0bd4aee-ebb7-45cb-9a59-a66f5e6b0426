<?php include 'app/views/shares/header.php'; ?>
<h1><PERSON><PERSON> s<PERSON>ch sản phẩm</h1>
<?php if(SessionHelper::isAdmin()): ?>
<a href="/lab01/product/add" class="btn btn-success mb-2">Thêm sản phẩm mới</a>
<?php endif; ?>
<ul class="list-group" id="product-list">
<!-- <PERSON><PERSON> sách sản phẩm sẽ được tải từ API và hiển thị tại đây -->
</ul>
<?php include 'app/views/shares/footer.php'; ?>
<script>
// Truyền thông tin quyền của người dùng vào JavaScript
const isAdmin = <?php echo SessionHelper::isAdmin() ? 'true' : 'false'; ?>;

document.addEventListener("DOMContentLoaded", function() {
fetch('/lab01/api/product')
.then(response => response.json())
.then(data => {

const productList = document.getElementById('product-list');
data.forEach(product => {
const productItem = document.createElement('li');
productItem.className = 'list-group-item';
let adminButtons = '';
if (isAdmin) {
	adminButtons = `
		<a href="/lab01/product/edit/${product.id}" class="btn btn-warning">Sửa</a>
		<button class="btn btn-danger" onclick="deleteProduct(${product.id})">Xóa</button>
	`;
}

productItem.innerHTML = `
<h2><a href="/lab01/product/show/${product.id}">${product.name}</a></h2>
<p>${product.description}</p>
<p>Giá: ${product.price} VND</p>
<p>Danh mục: ${product.category_name}</p>
<div class="mt-2">
    <a href="/lab01/product/addToCart/${product.id}" class="btn btn-primary">Thêm vào giỏ hàng</a>
    ${adminButtons}
</div>
`;
productList.appendChild(productItem);
});
});
});
function deleteProduct(id) {
	// Chỉ admin mới có thể xóa sản phẩm
	if (!isAdmin) {
		alert('Bạn không có quyền thực hiện chức năng này!');
		return;
	}

	if (confirm('Bạn có chắc chắn muốn xóa sản phẩm này?')) {
		fetch(`/lab01/api/product/${id}`, {
			method: 'DELETE'
		})
		.then(response => response.json())
		.then(data => {
			if (data.message === 'Product deleted successfully') {
				location.reload();
			} else {
				alert('Xóa sản phẩm thất bại: ' + (data.message || 'Lỗi không xác định'));
			}
		})
		.catch(error => {
			alert('Có lỗi xảy ra: ' + error.message);
		});
	}
}
</script>