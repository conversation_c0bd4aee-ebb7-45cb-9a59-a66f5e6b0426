<?php include 'app/views/shares/header.php'; ?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="text-primary">
                    <i class="fas fa-store"></i> <PERSON>h sách sản phẩm
                </h1>
                <?php if(SessionHelper::isAdmin()): ?>
                <a href="/lab01/product/add" class="btn btn-success">
                    <i class="fas fa-plus"></i> Thêm sản phẩm mới
                </a>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Loading spinner -->
    <div id="loading" class="text-center py-5">
        <div class="spinner-border text-primary" role="status">
            <span class="sr-only">Đang tải...</span>
        </div>
        <p class="mt-2"><PERSON><PERSON> tả<PERSON> danh sách sản phẩm...</p>
    </div>

    <!-- Product grid -->
    <div class="row" id="product-list">
        <!-- Danh sách sản phẩm sẽ được tải từ API và hiển thị tại đây -->
    </div>

    <!-- Empty state -->
    <div id="empty-state" class="text-center py-5" style="display: none;">
        <i class="fas fa-box-open fa-5x text-muted mb-3"></i>
        <h4 class="text-muted">Chưa có sản phẩm nào</h4>
        <p class="text-muted">Hãy thêm sản phẩm đầu tiên của bạn!</p>
        <?php if(SessionHelper::isAdmin()): ?>
        <a href="/lab01/product/add" class="btn btn-primary">
            <i class="fas fa-plus"></i> Thêm sản phẩm
        </a>
        <?php endif; ?>
    </div>
</div>
<?php include 'app/views/shares/footer.php'; ?>

<style>
.product-card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    border: none;
    border-radius: 10px;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
}

.card-img-container {
    position: relative;
    border-radius: 10px 10px 0 0;
    overflow: hidden;
}

.card-img-top {
    transition: transform 0.3s ease;
}

.product-card:hover .card-img-top {
    transform: scale(1.05);
}

.card-title a {
    color: #333;
    transition: color 0.2s ease;
}

.card-title a:hover {
    color: #007bff;
    text-decoration: none;
}

.btn-group .btn {
    border-radius: 0;
}

.btn-group .btn:first-child {
    border-radius: 0.25rem 0 0 0.25rem;
}

.btn-group .btn:last-child {
    border-radius: 0 0.25rem 0.25rem 0;
}

@media (max-width: 768px) {
    .col-lg-3, .col-md-4, .col-sm-6 {
        margin-bottom: 1rem;
    }
}

.spinner-border {
    width: 3rem;
    height: 3rem;
}

.alert {
    border-radius: 10px;
}
</style>

<script>
// Truyền thông tin quyền của người dùng vào JavaScript
const isAdmin = <?php echo SessionHelper::isAdmin() ? 'true' : 'false'; ?>;

document.addEventListener("DOMContentLoaded", function() {
    const loading = document.getElementById('loading');
    const productList = document.getElementById('product-list');
    const emptyState = document.getElementById('empty-state');

    fetch('/lab01/api/product')
    .then(response => response.json())
    .then(data => {
        loading.style.display = 'none';

        if (data.length === 0) {
            emptyState.style.display = 'block';
            return;
        }

        data.forEach(product => {
            const productCard = createProductCard(product);
            productList.appendChild(productCard);
        });
    })
    .catch(error => {
        loading.style.display = 'none';
        productList.innerHTML = `
            <div class="col-12">
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    Có lỗi xảy ra khi tải danh sách sản phẩm: ${error.message}
                </div>
            </div>
        `;
    });
});

function createProductCard(product) {
    const col = document.createElement('div');
    col.className = 'col-lg-3 col-md-4 col-sm-6 mb-4';

    // Xử lý hình ảnh
    const imageUrl = product.image ? `/${product.image}` : '/uploads/no-image.png';

    // Tạo nút admin
    let adminButtons = '';
    if (isAdmin) {
        adminButtons = `
            <div class="btn-group w-100 mt-2" role="group">
                <a href="/lab01/product/edit/${product.id}" class="btn btn-warning btn-sm">
                    <i class="fas fa-edit"></i> Sửa
                </a>
                <button class="btn btn-danger btn-sm" onclick="deleteProduct(${product.id})">
                    <i class="fas fa-trash"></i> Xóa
                </button>
            </div>
        `;
    }

    col.innerHTML = `
        <div class="card h-100 shadow-sm product-card">
            <div class="card-img-container" style="height: 200px; overflow: hidden;">
                <img src="${imageUrl}" class="card-img-top" alt="${product.name}"
                     style="width: 100%; height: 100%; object-fit: cover;"
                     onerror="this.src='/uploads/no-image.png'">
            </div>
            <div class="card-body d-flex flex-column">
                <h5 class="card-title text-truncate" title="${product.name}">
                    <a href="/lab01/product/show/${product.id}" class="text-decoration-none">
                        ${product.name}
                    </a>
                </h5>
                <p class="card-text text-muted small mb-2">
                    <i class="fas fa-tag"></i> ${product.category_name || 'Chưa phân loại'}
                </p>
                <p class="card-text flex-grow-1" style="display: -webkit-box; -webkit-line-clamp: 3; -webkit-box-orient: vertical; overflow: hidden;">
                    ${product.description || 'Không có mô tả'}
                </p>
                <div class="mt-auto">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="h5 text-primary mb-0">
                            <i class="fas fa-dollar-sign"></i> ${formatPrice(product.price)} VND
                        </span>
                    </div>
                    <a href="/lab01/product/addToCart/${product.id}" class="btn btn-primary w-100">
                        <i class="fas fa-shopping-cart"></i> Thêm vào giỏ
                    </a>
                    ${adminButtons}
                </div>
            </div>
        </div>
    `;

    return col;
}

function formatPrice(price) {
    return new Intl.NumberFormat('vi-VN').format(price);
}
function deleteProduct(id) {
	// Chỉ admin mới có thể xóa sản phẩm
	if (!isAdmin) {
		alert('Bạn không có quyền thực hiện chức năng này!');
		return;
	}

	if (confirm('Bạn có chắc chắn muốn xóa sản phẩm này?')) {
		fetch(`/lab01/api/product/${id}`, {
			method: 'DELETE'
		})
		.then(response => response.json())
		.then(data => {
			if (data.message === 'Product deleted successfully') {
				location.reload();
			} else {
				alert('Xóa sản phẩm thất bại: ' + (data.message || 'Lỗi không xác định'));
			}
		})
		.catch(error => {
			alert('Có lỗi xảy ra: ' + error.message);
		});
	}
}
</script>