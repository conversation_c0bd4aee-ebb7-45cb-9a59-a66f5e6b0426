<?php include_once 'app/views/shares/header.php'; ?>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <h2><i class="fas fa-user"></i> Chi tiết tài khoản</h2>
            
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        Thông tin tài khoản: <strong><?= htmlspecialchars($account->username) ?></strong>
                        <?php if ($account->username === $_SESSION['username']): ?>
                            <span class="badge badge-primary ml-2">Tài khoản của bạn</span>
                        <?php endif; ?>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>ID:</strong></td>
                                    <td><?= htmlspecialchars($account->id) ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Tên đăng nhập:</strong></td>
                                    <td><?= htmlspecialchars($account->username) ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Họ tên:</strong></td>
                                    <td><?= htmlspecialchars($account->fullname) ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Email:</strong></td>
                                    <td><?= htmlspecialchars($account->email) ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Email xác thực:</strong></td>
                                    <td><?= htmlspecialchars($account->verification_email) ?></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Số điện thoại:</strong></td>
                                    <td><?= htmlspecialchars($account->phone) ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Vai trò:</strong></td>
                                    <td>
                                        <?php if ($account->role === 'admin'): ?>
                                            <span class="badge badge-danger">Admin</span>
                                        <?php else: ?>
                                            <span class="badge badge-secondary">User</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Ngày tạo:</strong></td>
                                    <td><?= date('d/m/Y H:i:s', strtotime($account->created_at)) ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Cập nhật lần cuối:</strong></td>
                                    <td>
                                        <?php if ($account->updated_at): ?>
                                            <?= date('d/m/Y H:i:s', strtotime($account->updated_at)) ?>
                                        <?php else: ?>
                                            <em>Chưa cập nhật</em>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="btn-group" role="group">
                        <a href="/lab01/account/editAccount/<?= $account->id ?>" class="btn btn-warning">
                            <i class="fas fa-edit"></i> Chỉnh sửa
                        </a>
                        <?php if ($account->username !== $_SESSION['username']): ?>
                            <a href="/lab01/account/deleteAccount/<?= $account->id ?>" 
                               class="btn btn-danger"
                               onclick="return confirm('Bạn có chắc chắn muốn xóa tài khoản này?')">
                                <i class="fas fa-trash"></i> Xóa
                            </a>
                        <?php endif; ?>
                        <a href="/lab01/account/accountManagement" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Quay lại danh sách
                        </a>
                    </div>
                </div>
            </div>

            <!-- Thống kê đơn hàng nếu có -->
            <?php if ($account->role !== 'admin'): ?>
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-chart-bar"></i> Thống kê hoạt động</h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-4">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <h4>0</h4>
                                        <p>Tổng đơn hàng</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card bg-success text-white">
                                    <div class="card-body">
                                        <h4>0</h4>
                                        <p>Đơn hoàn thành</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card bg-warning text-white">
                                    <div class="card-body">
                                        <h4>0</h4>
                                        <p>Đơn chờ xử lý</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-info-circle"></i> 
                                Thống kê chi tiết sẽ được cập nhật khi có dữ liệu đơn hàng.
                            </small>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Font Awesome for icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

<?php include_once 'app/views/shares/footer.php'; ?>
