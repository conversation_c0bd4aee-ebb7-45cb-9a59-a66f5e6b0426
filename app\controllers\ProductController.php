<?php
// Require SessionHelper and other necessary files
require_once('app/config/database.php');
require_once('app/models/ProductModel.php');
require_once('app/models/CategoryModel.php');
class productController
{
private $productModel;
private $db;
public function __construct()
{
$this->db = (new Database())->getConnection();
$this->productModel = new productModel($this->db);

}
public function index()
{
$products = $this->productModel->getproducts();
include 'app/views/product/list.php';
}
public function show($id)
{
$product = $this->productModel->getproductById($id);
if ($product) {
include 'app/views/product/show.php';
} else {
echo "Không thấy sản phẩm.";
}
}
public function add()
{
$categories = (new CategoryModel($this->db))->getCategories();
include_once 'app/views/product/add.php';
}
public function save()
{
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
$name = $_POST['name'] ?? '';
$description = $_POST['description'] ?? '';
$price = $_POST['price'] ?? '';
$category_id = $_POST['category_id'] ?? null;
if (isset($_FILES['image']) && $_FILES['image']['error'] == 0) {
$image = $this->uploadImage($_FILES['image']);
} else {
$image = "";
}
$result = $this->productModel->addproduct($name, $description, $price,

$category_id, $image);

if (is_array($result)) {
$errors = $result;
$categories = (new CategoryModel($this->db))->getCategories();
include 'app/views/product/add.php';
} else {

header('Location: /lab01/product');
}
}
}
public function edit($id)
{
$product = $this->productModel->getproductById($id);
$categories = (new CategoryModel($this->db))->getCategories();
if ($product) {
include 'app/views/product/edit.php';
} else {
echo "Không thấy sản phẩm.";
}
}
public function update()
{
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
$id = $_POST['id'];
$name = $_POST['name'];
$description = $_POST['description'];
$price = $_POST['price'];
$category_id = $_POST['category_id'];
if (isset($_FILES['image']) && $_FILES['image']['error'] == 0) {
$image = $this->uploadImage($_FILES['image']);
} else {
$image = $_POST['existing_image'];
}
$edit = $this->productModel->updateproduct($id, $name, $description,

$price, $category_id, $image);
if ($edit) {
header('Location: /lab01/product');
} else {
echo "Đã xảy ra lỗi khi lưu sản phẩm.";
}
}
}
public function delete($id)
{
if ($this->productModel->deleteproduct($id)) {

header('Location: /lab01/product');
} else {
echo "Đã xảy ra lỗi khi xóa sản phẩm.";
}
}
private function uploadImage($file)
{
$target_dir = "uploads/";
// Kiểm tra và tạo thư mục nếu chưa tồn tại
if (!is_dir($target_dir)) {
mkdir($target_dir, 0777, true);
}
$target_file = $target_dir . basename($file["name"]);
$imageFileType = strtolower(pathinfo($target_file, PATHINFO_EXTENSION));
// Kiểm tra xem file có phải là hình ảnh không
$check = getimagesize($file["tmp_name"]);
if ($check === false) {
throw new Exception("File không phải là hình ảnh.");
}
// Kiểm tra kích thước file (10 MB = 10 * 1024 * 1024 bytes)
if ($file["size"] > 10 * 1024 * 1024) {
throw new Exception("Hình ảnh có kích thước quá lớn.");
}
// Chỉ cho phép một số định dạng hình ảnh nhất định
if ($imageFileType != "jpg" && $imageFileType != "png" && $imageFileType !=
"jpeg" && $imageFileType != "gif") {

throw new Exception("Chỉ cho phép các định dạng JPG, JPEG, PNG và GIF.");
}
// Lưu file
if (!move_uploaded_file($file["tmp_name"], $target_file)) {
throw new Exception("Có lỗi xảy ra khi tải lên hình ảnh.");
}
return $target_file;
}
public function addToCart($id)
{
$product = $this->productModel->getproductById($id);
if (!$product) {

echo "Không tìm thấy sản phẩm.";
return;
}
if (!isset($_SESSION['cart'])) {
$_SESSION['cart'] = [];
}
if (isset($_SESSION['cart'][$id])) {
$_SESSION['cart'][$id]['quantity']++;
} else {
$_SESSION['cart'][$id] = [
'name' => $product->name,
'price' => $product->price,
'quantity' => 1,
'image' => $product->image
];
}
header('Location: /lab01/product/cart');
}
public function cart()
{
$cart = isset($_SESSION['cart']) ? $_SESSION['cart'] : [];
include 'app/views/product/cart.php';
}
public function checkout()
{
include 'app/views/product/checkout.php';
}
public function processCheckout()
{
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
$name = $_POST['name'];
$phone = $_POST['phone'];
$address = $_POST['address'];

// Lấy user_id nếu đã đăng nhập
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

$user_id = null;
if (isset($_SESSION['username'])) {
    // Lấy thông tin user từ database
    require_once('app/models/AccountModel.php');
    $accountModel = new AccountModel($this->db);
    $account = $accountModel->getAccountByUsername($_SESSION['username']);
    if ($account) {
        $user_id = $account->id;
    }
}

// Kiểm tra giỏ hàng
if (!isset($_SESSION['cart']) || empty($_SESSION['cart'])) {
echo "Giỏ hàng trống.";
return;
}
// Bắt đầu giao dịch

$this->db->beginTransaction();
try {
// Lưu thông tin đơn hàng vào bảng orders với user_id
$query = "INSERT INTO orders (user_id, name, phone, address, status, order_date) VALUES (:user_id, :name, :phone, :address, 'pending', NOW())";

$stmt = $this->db->prepare($query);
$stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
$stmt->bindParam(':name', $name);
$stmt->bindParam(':phone', $phone);
$stmt->bindParam(':address', $address);
$stmt->execute();
$order_id = $this->db->lastInsertId();
// Lưu chi tiết đơn hàng vào bảng order_details
$cart = $_SESSION['cart'];
foreach ($cart as $product_id => $item) {
$query = "INSERT INTO order_details (order_id, product_id,

quantity, price) VALUES (:order_id, :product_id, :quantity, :price * :quantity)";

$stmt = $this->db->prepare($query);
$stmt->bindParam(':order_id', $order_id);
$stmt->bindParam(':product_id', $product_id);
$stmt->bindParam(':quantity', $item['quantity']);
$stmt->bindParam(':price', $item['price']);
$stmt->execute();
}
// Xóa giỏ hàng sau khi đặt hàng thành công
unset($_SESSION['cart']);
// Commit giao dịch
$this->db->commit();
// Chuyển hướng đến trang xác nhận đơn hàng
header('Location: /lab01/product/orderConfirmation');
} catch (Exception $e) {
// Rollback giao dịch nếu có lỗi
$this->db->rollBack();
echo "Đã xảy ra lỗi khi xử lý đơn hàng: " . $e->getMessage();
}
}
}
public function orderConfirmation()
{
include 'app/views/product/orderConfirmation.php';
}


public function updateCart()
{
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $product_id = $_POST['product_id'] ?? null;
        $action = $_POST['action'] ?? null;

        if (!$product_id || !isset($_SESSION['cart'][$product_id])) {
            header('Location: /lab01/product/cart');
            exit;
        }

        switch ($action) {
            case 'increase':
                $_SESSION['cart'][$product_id]['quantity']++;
                break;
            case 'decrease':
                $_SESSION['cart'][$product_id]['quantity']--;
                // Nếu số lượng <= 0 thì xóa khỏi giỏ hàng
                if ($_SESSION['cart'][$product_id]['quantity'] <= 0) {
                    unset($_SESSION['cart'][$product_id]);
                }
                break;
        }
    }
    header('Location: /lab01/product/cart');
    exit;
}

}


?>