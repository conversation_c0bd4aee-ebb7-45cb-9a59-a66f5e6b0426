<?php
require_once('app/config/database.php');
require_once('app/models/CategoryModel.php');
require_once('app/helpers/SessionHelper.php');

class CategoryController
{
    private $categoryModel;
    private $db;

    public function __construct()
    {
        $this->db = (new Database())->getConnection();
        $this->categoryModel = new CategoryModel($this->db);
    }

    public function list()
    {
        $categories = $this->categoryModel->getCategories();
        include 'app/views/category/list.php';
    }

    public function add()
    {
        // Chỉ admin mới được thêm danh mục
        SessionHelper::requireAdmin();

        $errors = [];
        include 'app/views/category/add.php';
    }

    public function save()
    {
        // Chỉ admin mới được lưu danh mục
        SessionHelper::requireAdmin();

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $name = $_POST['name'] ?? '';
            $description = $_POST['description'] ?? '';

            $result = $this->categoryModel->addCategory($name, $description);

            if (is_array($result)) {
                // Lỗi validate
                $errors = $result;
                include 'app/views/category/add.php';
            } elseif ($result === true) {
                header('Location: /lab01/category/list');
                exit;
            } else {
                echo "Đã xảy ra lỗi khi thêm danh mục.";
            }
        }
    }

    public function edit($id)
    {
        // Chỉ admin mới được sửa danh mục
        SessionHelper::requireAdmin();

        $category = $this->categoryModel->getCategoryById($id);
        if ($category) {
            $errors = [];
            include 'app/views/category/edit.php';
        } else {
            echo "Không tìm thấy danh mục.";
        }
    }

    public function update()
    {
        // Chỉ admin mới được cập nhật danh mục
        SessionHelper::requireAdmin();

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $id = $_POST['id'] ?? null;
            $name = $_POST['name'] ?? '';
            $description = $_POST['description'] ?? '';

            $result = $this->categoryModel->updateCategory($id, $name, $description);

            if ($result === true) {
                header('Location: /lab01/category/list');
                exit;
            } else {
                // Nếu update thất bại, hoặc lỗi validate thì load lại form với lỗi
                $category = $this->categoryModel->getCategoryById($id);
                $errors = is_array($result) ? $result : ['update' => 'Đã xảy ra lỗi khi cập nhật danh mục.'];
                include 'app/views/category/edit.php';
            }
        }
    }

    public function delete($id)
    {
        // Chỉ admin mới được xóa danh mục
        SessionHelper::requireAdmin();

        if ($this->categoryModel->hasproducts($id)) {
            echo "<script>alert('Không thể xóa danh mục vì còn sản phẩm liên quan!'); window.location.href='/lab01/category/list';</script>";
            exit;
        }

        if ($this->categoryModel->deleteCategory($id)) {
            header('Location: /lab01/category/list');
            exit;
        } else {
            echo "Đã xảy ra lỗi khi xóa danh mục.";
        }
    }
    



}
?>
