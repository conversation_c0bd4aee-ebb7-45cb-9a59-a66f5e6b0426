<?php
class OrderModel {
    private $conn;
    private $table_name = "orders";
    
    public function __construct($db) {
        $this->conn = $db;
    }
    
    // L<PERSON>y đơn hàng theo user_id
    public function getOrdersByUser($userId) {
        $query = "SELECT o.*,
                         COALESCE(SUM(od.quantity * od.price), 0) as total
                  FROM " . $this->table_name . " o
                  LEFT JOIN order_details od ON o.id = od.order_id
                  WHERE o.user_id = :user_id
                  GROUP BY o.id, o.name, o.phone, o.address, o.status, o.order_date, o.user_id
                  ORDER BY o.order_date DESC";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_OBJ);
    }
    
    // L<PERSON>y đơn hàng theo ID
    public function getOrderById($orderId) {
        $query = "SELECT o.*,
                         COALESCE(SUM(od.quantity * od.price), 0) as total
                  FROM " . $this->table_name . " o
                  LEFT JOIN order_details od ON o.id = od.order_id
                  WHERE o.id = :id
                  GROUP BY o.id, o.name, o.phone, o.address, o.status, o.order_date, o.user_id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $orderId, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_OBJ);
    }
    
    // Lấy tất cả đơn hàng (cho admin)
    public function getAllOrders() {
        $query = "SELECT o.*,
                         a.username, a.fullname,
                         COALESCE(SUM(od.quantity * od.price), 0) as total
                  FROM " . $this->table_name . " o
                  LEFT JOIN account a ON o.user_id = a.id
                  LEFT JOIN order_details od ON o.id = od.order_id
                  GROUP BY o.id, o.user_id, o.name, o.phone, o.address, o.status, o.order_date, a.username, a.fullname
                  ORDER BY o.order_date DESC";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_OBJ);
    }
    
    // Cập nhật trạng thái đơn hàng
    public function updateOrderStatus($orderId, $status) {
        $query = "UPDATE " . $this->table_name . " SET status = :status WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':status', $status);
        $stmt->bindParam(':id', $orderId);
        return $stmt->execute();
    }
    
    // Lấy chi tiết đơn hàng
    public function getOrderDetails($orderId) {
        $query = "SELECT od.*, p.name as product_name, p.image
                  FROM order_details od
                  LEFT JOIN product p ON od.product_id = p.id
                  WHERE od.order_id = :order_id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':order_id', $orderId);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_OBJ);
    }
}
?>
