<?php
class OrderModel {
    private $conn;
    private $table_name = "orders";
    
    public function __construct($db) {
        $this->conn = $db;
    }
    
    // L<PERSON>y đơn hàng theo user_id
    public function getOrdersByUser($userId) {
        $query = "SELECT o.*,
                         COALESCE(SUM(od.quantity * od.price), 0) as total
                  FROM " . $this->table_name . " o
                  LEFT JOIN order_details od ON o.id = od.order_id
                  WHERE o.user_id = :user_id
                  GROUP BY o.id, o.name, o.phone, o.address, o.status, o.order_date, o.user_id
                  ORDER BY o.order_date DESC";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_OBJ);
    }
    
    // L<PERSON>y đơn hàng theo ID
    public function getOrderById($orderId) {
        $query = "SELECT o.*,
                         COALESCE(SUM(od.quantity * od.price), 0) as total
                  FROM " . $this->table_name . " o
                  LEFT JOIN order_details od ON o.id = od.order_id
                  WHERE o.id = :id
                  GROUP BY o.id, o.name, o.phone, o.address, o.status, o.order_date, o.user_id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $orderId, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_OBJ);
    }
    
    // Lấy tất cả đơn hàng (cho admin)
    public function getAllOrders() {
        // Kiểm tra xem bảng có các cột cần thiết không
        try {
            $checkQuery = "SHOW COLUMNS FROM " . $this->table_name . " LIKE 'status'";
            $checkStmt = $this->conn->prepare($checkQuery);
            $checkStmt->execute();
            $hasStatus = $checkStmt->rowCount() > 0;

            $checkQuery2 = "SHOW COLUMNS FROM " . $this->table_name . " LIKE 'user_id'";
            $checkStmt2 = $this->conn->prepare($checkQuery2);
            $checkStmt2->execute();
            $hasUserId = $checkStmt2->rowCount() > 0;

            $checkQuery3 = "SHOW COLUMNS FROM " . $this->table_name . " LIKE 'order_date'";
            $checkStmt3 = $this->conn->prepare($checkQuery3);
            $checkStmt3->execute();
            $hasOrderDate = $checkStmt3->rowCount() > 0;

            if ($hasStatus && $hasUserId && $hasOrderDate) {
                // Có đầy đủ cột mới
                $query = "SELECT o.*,
                                 a.username, a.fullname,
                                 COALESCE(SUM(od.quantity * od.price), 0) as total
                          FROM " . $this->table_name . " o
                          LEFT JOIN account a ON o.user_id = a.id
                          LEFT JOIN order_details od ON o.id = od.order_id
                          GROUP BY o.id, o.user_id, o.name, o.phone, o.address, o.status, o.order_date, a.username, a.fullname
                          ORDER BY o.order_date DESC";
            } else {
                // Dùng cấu trúc cũ
                $query = "SELECT o.*,
                                 COALESCE(SUM(od.quantity * od.price), 0) as total
                          FROM " . $this->table_name . " o
                          LEFT JOIN order_details od ON o.id = od.order_id
                          GROUP BY o.id, o.name, o.phone, o.address, o.created_at
                          ORDER BY o.created_at DESC";
            }

            $stmt = $this->conn->prepare($query);
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_OBJ);

        } catch (Exception $e) {
            // Fallback về query đơn giản nhất
            $query = "SELECT o.*, COALESCE(SUM(od.quantity * od.price), 0) as total
                      FROM " . $this->table_name . " o
                      LEFT JOIN order_details od ON o.id = od.order_id
                      GROUP BY o.id
                      ORDER BY o.id DESC";
            $stmt = $this->conn->prepare($query);
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_OBJ);
        }
    }
    
    // Cập nhật trạng thái đơn hàng
    public function updateOrderStatus($orderId, $status) {
        $query = "UPDATE " . $this->table_name . " SET status = :status WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':status', $status);
        $stmt->bindParam(':id', $orderId);
        return $stmt->execute();
    }
    
    // Lấy chi tiết đơn hàng
    public function getOrderDetails($orderId) {
        $query = "SELECT od.*, p.name as product_name, p.image
                  FROM order_details od
                  LEFT JOIN product p ON od.product_id = p.id
                  WHERE od.order_id = :order_id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':order_id', $orderId);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_OBJ);
    }
}
?>
