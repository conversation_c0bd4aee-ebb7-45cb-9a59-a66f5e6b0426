<?php include_once 'app/views/shares/header.php'; ?>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <h2><i class="fas fa-user-edit"></i> Chỉnh sửa tài kho<PERSON>n</h2>
            
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        Chỉnh sửa thông tin: <strong><?= htmlspecialchars($account->username) ?></strong>
                        <?php if ($account->username === $_SESSION['username']): ?>
                            <span class="badge badge-primary ml-2">Tài khoản của bạn</span>
                        <?php endif; ?>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($errors)): ?>
                        <div class="alert alert-danger">
                            <h6><i class="fas fa-exclamation-triangle"></i> Có lỗi xảy ra:</h6>
                            <ul class="mb-0">
                                <?php foreach ($errors as $error): ?>
                                    <li><?= htmlspecialchars($error) ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <form method="POST" action="/lab01/account/updateAccount/<?= $account->id ?>">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="username"><i class="fas fa-user"></i> Tên đăng nhập *</label>
                                    <input type="text" 
                                           class="form-control <?= isset($errors['username']) ? 'is-invalid' : '' ?>" 
                                           id="username" 
                                           name="username" 
                                           value="<?= htmlspecialchars($account->username) ?>" 
                                           required>
                                    <?php if (isset($errors['username'])): ?>
                                        <div class="invalid-feedback"><?= $errors['username'] ?></div>
                                    <?php endif; ?>
                                </div>

                                <div class="form-group">
                                    <label for="fullname"><i class="fas fa-id-card"></i> Họ tên *</label>
                                    <input type="text" 
                                           class="form-control <?= isset($errors['fullname']) ? 'is-invalid' : '' ?>" 
                                           id="fullname" 
                                           name="fullname" 
                                           value="<?= htmlspecialchars($account->fullname) ?>" 
                                           required>
                                    <?php if (isset($errors['fullname'])): ?>
                                        <div class="invalid-feedback"><?= $errors['fullname'] ?></div>
                                    <?php endif; ?>
                                </div>

                                <div class="form-group">
                                    <label for="email"><i class="fas fa-envelope"></i> Email *</label>
                                    <input type="email" 
                                           class="form-control <?= isset($errors['email']) ? 'is-invalid' : '' ?>" 
                                           id="email" 
                                           name="email" 
                                           value="<?= htmlspecialchars($account->email) ?>" 
                                           required>
                                    <?php if (isset($errors['email'])): ?>
                                        <div class="invalid-feedback"><?= $errors['email'] ?></div>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="verification_email"><i class="fas fa-envelope-open"></i> Email xác thực *</label>
                                    <input type="email" 
                                           class="form-control <?= isset($errors['verification_email']) ? 'is-invalid' : '' ?>" 
                                           id="verification_email" 
                                           name="verification_email" 
                                           value="<?= htmlspecialchars($account->verification_email) ?>" 
                                           required>
                                    <?php if (isset($errors['verification_email'])): ?>
                                        <div class="invalid-feedback"><?= $errors['verification_email'] ?></div>
                                    <?php endif; ?>
                                </div>

                                <div class="form-group">
                                    <label for="phone"><i class="fas fa-phone"></i> Số điện thoại</label>
                                    <input type="text" 
                                           class="form-control <?= isset($errors['phone']) ? 'is-invalid' : '' ?>" 
                                           id="phone" 
                                           name="phone" 
                                           value="<?= htmlspecialchars($account->phone) ?>">
                                    <?php if (isset($errors['phone'])): ?>
                                        <div class="invalid-feedback"><?= $errors['phone'] ?></div>
                                    <?php endif; ?>
                                </div>

                                <div class="form-group">
                                    <label for="role"><i class="fas fa-user-tag"></i> Vai trò *</label>
                                    <select class="form-control" id="role" name="role" required>
                                        <option value="user" <?= $account->role === 'user' ? 'selected' : '' ?>>User</option>
                                        <option value="admin" <?= $account->role === 'admin' ? 'selected' : '' ?>>Admin</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i> 
                                <strong>Lưu ý:</strong> Để thay đổi mật khẩu, vui lòng sử dụng chức năng "Quên mật khẩu".
                            </div>
                        </div>

                        <div class="form-group">
                            <small class="text-muted">
                                <i class="fas fa-calendar-alt"></i> 
                                Tài khoản được tạo: <?= date('d/m/Y H:i:s', strtotime($account->created_at)) ?>
                                <?php if ($account->updated_at): ?>
                                    <br>
                                    <i class="fas fa-edit"></i> 
                                    Cập nhật lần cuối: <?= date('d/m/Y H:i:s', strtotime($account->updated_at)) ?>
                                <?php endif; ?>
                            </small>
                        </div>

                        <div class="form-group text-center">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Cập nhật tài khoản
                            </button>
                            <a href="/lab01/account/accountDetail/<?= $account->id ?>" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Hủy
                            </a>
                            <a href="/lab01/account/accountManagement" class="btn btn-info">
                                <i class="fas fa-list"></i> Danh sách tài khoản
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Font Awesome for icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

<?php include_once 'app/views/shares/footer.php'; ?>
