<?php include 'app/views/shares/header.php'; ?>
<div class="container py-5">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>Chi tiết đơn hàng #<?= htmlspecialchars($order->id) ?></h2>
                <a href="/lab01/account/orderManagement" class="btn btn-secondary">Quay lại</a>
            </div>
            
            <div class="row">
                <!-- Thông tin đơn hàng -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5>Thông tin đơn hàng</h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Mã đơn hàng:</strong></td>
                                    <td>#<?= htmlspecialchars($order->id) ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Ngày đặt:</strong></td>
                                    <td><?= date('d/m/Y H:i:s', strtotime($order->order_date)) ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Trạng thái:</strong></td>
                                    <td>
                                        <?php 
                                        $statusLabels = [
                                            'pending' => ['label' => 'Chờ xử lý', 'class' => 'warning'],
                                            'processing' => ['label' => 'Đang xử lý', 'class' => 'info'],
                                            'shipped' => ['label' => 'Đã gửi hàng', 'class' => 'primary'],
                                            'delivered' => ['label' => 'Đã giao hàng', 'class' => 'success'],
                                            'cancelled' => ['label' => 'Đã hủy', 'class' => 'danger']
                                        ];
                                        $status = $statusLabels[$order->status] ?? ['label' => $order->status, 'class' => 'secondary'];
                                        ?>
                                        <span class="badge badge-<?= $status['class'] ?>"><?= $status['label'] ?></span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Tổng tiền:</strong></td>
                                    <td><strong><?= number_format($order->total, 0, ',', '.') ?> VNĐ</strong></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
                
                <!-- Thông tin người nhận -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5>Thông tin người nhận</h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Họ tên:</strong></td>
                                    <td><?= htmlspecialchars($order->name) ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Số điện thoại:</strong></td>
                                    <td><?= htmlspecialchars($order->phone) ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Địa chỉ:</strong></td>
                                    <td><?= htmlspecialchars($order->address) ?></td>
                                </tr>
                                <?php if (isset($order->username) && $order->username): ?>
                                <tr>
                                    <td><strong>Tài khoản:</strong></td>
                                    <td><?= htmlspecialchars($order->username) ?></td>
                                </tr>
                                <?php endif; ?>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Chi tiết sản phẩm -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5>Chi tiết sản phẩm</h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($orderDetails)): ?>
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>Hình ảnh</th>
                                        <th>Tên sản phẩm</th>
                                        <th>Số lượng</th>
                                        <th>Đơn giá</th>
                                        <th>Thành tiền</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach($orderDetails as $detail): ?>
                                        <tr>
                                            <td>
                                                <?php if ($detail->image): ?>
                                                    <img src="/lab01/public/images/<?= htmlspecialchars($detail->image) ?>" 
                                                         alt="<?= htmlspecialchars($detail->product_name) ?>" 
                                                         style="width: 50px; height: 50px; object-fit: cover;">
                                                <?php else: ?>
                                                    <div style="width: 50px; height: 50px; background-color: #f8f9fa; display: flex; align-items: center; justify-content: center;">
                                                        <small>No image</small>
                                                    </div>
                                                <?php endif; ?>
                                            </td>
                                            <td><?= htmlspecialchars($detail->product_name) ?></td>
                                            <td><?= htmlspecialchars($detail->quantity) ?></td>
                                            <td><?= number_format($detail->price, 0, ',', '.') ?> VNĐ</td>
                                            <td><?= number_format($detail->quantity * $detail->price, 0, ',', '.') ?> VNĐ</td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                                <tfoot>
                                    <tr>
                                        <th colspan="4" class="text-right">Tổng cộng:</th>
                                        <th><?= number_format($order->total, 0, ',', '.') ?> VNĐ</th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    <?php else: ?>
                        <p class="text-center">Không có chi tiết sản phẩm.</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php include 'app/views/shares/footer.php'; ?>
