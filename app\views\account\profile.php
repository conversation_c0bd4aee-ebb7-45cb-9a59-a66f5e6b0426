<?php include 'app/views/shares/header.php'; ?>
<div class="container py-5">
    <div class="row">
        <div class="col-md-8 mx-auto">
            <h2 class="mb-4">Thông tin tài khoản</h2>
            
            <div class="card">
                <div class="card-header">
                    <h5>Thông tin cá nhân</h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>Tên đăng nhập:</strong></td>
                            <td><?= htmlspecialchars($account->username) ?></td>
                        </tr>
                        <tr>
                            <td><strong>Họ tên:</strong></td>
                            <td><?= htmlspecialchars($account->fullname) ?></td>
                        </tr>
                        <tr>
                            <td><strong>Email:</strong></td>
                            <td><?= htmlspecialchars($account->email) ?></td>
                        </tr>
                        <tr>
                            <td><strong><PERSON><PERSON> điện thoại:</strong></td>
                            <td><?= htmlspecialchars($account->phone) ?></td>
                        </tr>
                        <tr>
                            <td><strong>Vai trò:</strong></td>
                            <td>
                                <?php
                                $roleLabels = [
                                    'user' => 'Người dùng',
                                    'admin' => 'Quản trị viên'
                                ];
                                echo $roleLabels[$account->role] ?? $account->role;
                                ?>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>Ngày tạo:</strong></td>
                            <td><?= date('d/m/Y H:i:s', strtotime($account->created_at)) ?></td>
                        </tr>
                    </table>
                </div>
            </div>

            <!-- Form đổi mật khẩu -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5>Đổi mật khẩu</h5>
                </div>
                <div class="card-body">
                    <?php if (isset($passwordSuccess)): ?>
                        <div class="alert alert-success"><?= $passwordSuccess ?></div>
                    <?php endif; ?>

                    <?php if (isset($passwordErrors) && !empty($passwordErrors)): ?>
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                <?php foreach ($passwordErrors as $error): ?>
                                    <li><?= htmlspecialchars($error) ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <form method="POST" action="/lab01/account/changePassword">
                        <div class="form-group mb-3">
                            <label for="current_password">Mật khẩu hiện tại:</label>
                            <input type="password" class="form-control" id="current_password" name="current_password" required>
                        </div>

                        <div class="form-group mb-3">
                            <label for="new_password">Mật khẩu mới:</label>
                            <input type="password" class="form-control" id="new_password" name="new_password" required minlength="6">
                            <small class="form-text text-muted">Mật khẩu phải có ít nhất 6 ký tự</small>
                        </div>

                        <div class="form-group mb-3">
                            <label for="confirm_password">Xác nhận mật khẩu mới:</label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                        </div>

                        <button type="submit" class="btn btn-warning">Đổi mật khẩu</button>
                    </form>
                </div>
            </div>

            <div class="mt-4">
                <a href="/lab01/product" class="btn btn-primary">Quay lại trang chủ</a>
                <a href="/lab01/account/logout" class="btn btn-secondary">Đăng xuất</a>
            </div>
        </div>
    </div>
</div>
<?php include 'app/views/shares/footer.php'; ?>
