<?php
class AccountModel
{
    private $conn;
    private $table_name = "account";
    public function __construct($db)
    {
        $this->conn = $db;
    }

    public function getAccountByUsername($username)
    {
        $query = "SELECT *, ROLE as role FROM account WHERE username = :username";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':username', $username, PDO::PARAM_STR);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_OBJ);
        return $result;
    }

    public function getAccountByEmail($email)
    {
        $query = "SELECT * FROM account WHERE email = :email";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':email', $email, PDO::PARAM_STR);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_OBJ);
        return $result;
    }

    public function saveaccount($username, $fullname, $email, $verificationEmail, $phone, $password, $role="user"){
        $query = "INSERT INTO " . $this->table_name . " (username, fullname, email, verification_email, phone, password, role)
        VALUES (:username, :fullname, :email, :verification_email, :phone, :password, :role)";
        $stmt = $this->conn->prepare($query);
        // Làm sạch dữ liệu
        $fullname = htmlspecialchars(strip_tags($fullname));
        $username = htmlspecialchars(strip_tags($username));
        $email = htmlspecialchars(strip_tags($email));
        $verificationEmail = htmlspecialchars(strip_tags($verificationEmail));
        $phone = htmlspecialchars(strip_tags($phone));
        // Gán dữ liệu vào câu lệnh
        $stmt->bindParam(':username', $username);
        $stmt->bindParam(':fullname', $fullname);
        $stmt->bindParam(':email', $email);
        $stmt->bindParam(':verification_email', $verificationEmail);
        $stmt->bindParam(':phone', $phone);
        $stmt->bindParam(':password', $password);
        $stmt->bindParam(':role', $role);
        // Thực thi câu lệnh
        if ($stmt->execute()) {
            return true;
        }
        return false;
    }

    public function updatePassword($username, $newHashedPassword) {
        $sql = "UPDATE account SET password = :password WHERE username = :username";
        $stmt = $this->conn->prepare($sql);
        $stmt->bindParam(':password', $newHashedPassword);
        $stmt->bindParam(':username', $username);
        return $stmt->execute();
    }

    public function getAllAccounts() {
        $query = "SELECT id, username, fullname, email, verification_email, phone, role FROM " . $this->table_name . " ORDER BY id DESC";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_OBJ);
    }

    // Xác thực username và verification_email
    public function verifyUsernameAndEmail($username, $verificationEmail) {
        $query = "SELECT * FROM " . $this->table_name . " WHERE username = :username AND verification_email = :verification_email";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':username', $username, PDO::PARAM_STR);
        $stmt->bindParam(':verification_email', $verificationEmail, PDO::PARAM_STR);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_OBJ);
    }

    // Lấy tài khoản theo ID
    public function getAccountById($id) {
        $query = "SELECT *, ROLE as role FROM " . $this->table_name . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_OBJ);
    }

    // Cập nhật tài khoản
    public function updateAccount($id, $username, $fullname, $email, $verificationEmail, $phone, $role) {
        $query = "UPDATE " . $this->table_name . " SET
                  username = :username,
                  fullname = :fullname,
                  email = :email,
                  verification_email = :verification_email,
                  phone = :phone,
                  ROLE = :role,
                  updated_at = CURRENT_TIMESTAMP
                  WHERE id = :id";

        $stmt = $this->conn->prepare($query);

        // Làm sạch dữ liệu
        $username = htmlspecialchars(strip_tags($username));
        $fullname = htmlspecialchars(strip_tags($fullname));
        $email = htmlspecialchars(strip_tags($email));
        $verificationEmail = htmlspecialchars(strip_tags($verificationEmail));
        $phone = htmlspecialchars(strip_tags($phone));
        $role = htmlspecialchars(strip_tags($role));

        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->bindParam(':username', $username);
        $stmt->bindParam(':fullname', $fullname);
        $stmt->bindParam(':email', $email);
        $stmt->bindParam(':verification_email', $verificationEmail);
        $stmt->bindParam(':phone', $phone);
        $stmt->bindParam(':role', $role);

        return $stmt->execute();
    }

    // Xóa tài khoản
    public function deleteAccount($id) {
        $query = "DELETE FROM " . $this->table_name . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        return $stmt->execute();
    }
}