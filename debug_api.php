<?php
// Debug API để kiểm tra dữ liệu sản phẩm
session_start();

// <PERSON><PERSON><PERSON> lập đăng nhập admin
$_SESSION['username'] = 'q';
$_SESSION['role'] = 'admin';

echo "<h2>Debug API Products</h2>";

// Test database connection và lấy dữ liệu trự<PERSON> tiếp
try {
    require_once('app/config/database.php');
    require_once('app/models/ProductModel.php');
    
    $db = (new Database())->getConnection();
    $productModel = new ProductModel($db);
    
    echo "<h3>1. Test Database Query trực tiếp:</h3>";
    $stmt = $db->prepare("SELECT p.*, c.name as category_name FROM product p LEFT JOIN category c ON p.category_id = c.id");
    $stmt->execute();
    $products = $stmt->fetchAll(PDO::FETCH_OBJ);
    
    echo "<pre>";
    print_r($products);
    echo "</pre>";
    
    echo "<h3>2. Test ProductModel->getProducts():</h3>";
    $products2 = $productModel->getProducts();
    echo "<pre>";
    print_r($products2);
    echo "</pre>";
    
    echo "<h3>3. Test API Response:</h3>";
    // Simulate API call
    ob_start();
    require_once('app/controllers/ProductApiController.php');
    $apiController = new ProductApiController();
    $apiController->index();
    $apiResponse = ob_get_clean();
    
    echo "<p><strong>API Response:</strong></p>";
    echo "<pre>" . htmlspecialchars($apiResponse) . "</pre>";
    
    echo "<h3>4. Kiểm tra từng sản phẩm có hình ảnh:</h3>";
    foreach ($products as $product) {
        echo "<div style='border: 1px solid #ccc; margin: 10px; padding: 10px;'>";
        echo "<h4>Product ID: {$product->id}</h4>";
        echo "<p><strong>Name:</strong> {$product->name}</p>";
        echo "<p><strong>Image:</strong> " . ($product->image ?: 'NULL') . "</p>";
        
        if ($product->image) {
            $imagePath = $product->image;
            $fullPath = __DIR__ . '/' . $imagePath;
            echo "<p><strong>Image Path:</strong> {$imagePath}</p>";
            echo "<p><strong>Full Path:</strong> {$fullPath}</p>";
            echo "<p><strong>File exists:</strong> " . (file_exists($fullPath) ? "✓ Yes" : "✗ No") . "</p>";
            
            if (file_exists($fullPath)) {
                echo "<p><strong>Preview:</strong></p>";
                echo "<img src='/lab01/{$imagePath}' style='max-width: 200px; border: 1px solid #ddd;'>";
            }
        }
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

echo "<h3>5. Test JavaScript fetch:</h3>";
?>
<script>
console.log('Testing API fetch...');
fetch('/lab01/api/product')
    .then(response => response.json())
    .then(data => {
        console.log('API Response:', data);
        
        const container = document.getElementById('js-test-result');
        container.innerHTML = '<h4>JavaScript API Response:</h4><pre>' + JSON.stringify(data, null, 2) + '</pre>';
        
        // Test hiển thị hình ảnh
        data.forEach(product => {
            if (product.image) {
                const img = document.createElement('img');
                img.src = `/lab01/${product.image}`;
                img.style.maxWidth = '100px';
                img.style.margin = '5px';
                img.style.border = '1px solid #ddd';
                img.onerror = function() {
                    this.style.border = '2px solid red';
                    this.alt = 'Image not found: ' + product.image;
                };
                container.appendChild(img);
            }
        });
    })
    .catch(error => {
        console.error('API Error:', error);
        document.getElementById('js-test-result').innerHTML = '<p style="color: red;">API Error: ' + error.message + '</p>';
    });
</script>

<div id="js-test-result">Loading JavaScript test...</div>

<p><a href="/lab01/product">Đi đến danh sách sản phẩm</a></p>
