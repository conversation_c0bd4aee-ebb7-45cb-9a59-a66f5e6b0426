<?php include_once 'app/views/shares/header.php'; ?>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <h2><i class="fas fa-users"></i> Quản lý tài khoản</h2>
            
            <?php if (isset($_GET['success'])): ?>
                <?php if ($_GET['success'] == '1'): ?>
                    <div class="alert alert-success">Cập nhật tài khoản thành công!</div>
                <?php elseif ($_GET['success'] == 'delete'): ?>
                    <div class="alert alert-success">Xóa tài khoản thành công!</div>
                <?php endif; ?>
            <?php endif; ?>

            <?php if (isset($_GET['error'])): ?>
                <?php if ($_GET['error'] == '1'): ?>
                    <div class="alert alert-danger">Có lỗi xảy ra khi cập nhật tài khoản!</div>
                <?php elseif ($_GET['error'] == 'delete'): ?>
                    <div class="alert alert-danger">Có lỗi xảy ra khi xóa tài khoản!</div>
                <?php elseif ($_GET['error'] == 'self_delete'): ?>
                    <div class="alert alert-warning">Không thể xóa chính tài khoản của mình!</div>
                <?php endif; ?>
            <?php endif; ?>

            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Danh sách tài khoản</h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($accounts)): ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="thead-dark">
                                    <tr>
                                        <th>ID</th>
                                        <th>Tên đăng nhập</th>
                                        <th>Họ tên</th>
                                        <th>Email</th>
                                        <th>Email xác thực</th>
                                        <th>Số điện thoại</th>
                                        <th>Vai trò</th>
                                        <th>Ngày tạo</th>
                                        <th>Thao tác</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($accounts as $account): ?>
                                        <tr>
                                            <td><?= htmlspecialchars($account->id) ?></td>
                                            <td>
                                                <strong><?= htmlspecialchars($account->username) ?></strong>
                                                <?php if ($account->username === $_SESSION['username']): ?>
                                                    <span class="badge badge-primary ml-1">Bạn</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?= htmlspecialchars($account->fullname) ?></td>
                                            <td><?= htmlspecialchars($account->email) ?></td>
                                            <td><?= htmlspecialchars($account->verification_email) ?></td>
                                            <td><?= htmlspecialchars($account->phone) ?></td>
                                            <td>
                                                <?php if ($account->role === 'admin'): ?>
                                                    <span class="badge badge-danger">Admin</span>
                                                <?php else: ?>
                                                    <span class="badge badge-secondary">User</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?= date('d/m/Y H:i', strtotime($account->created_at)) ?></td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="/lab01/account/accountDetail/<?= $account->id ?>" 
                                                       class="btn btn-info btn-sm" title="Xem chi tiết">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="/lab01/account/editAccount/<?= $account->id ?>" 
                                                       class="btn btn-warning btn-sm" title="Chỉnh sửa">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <?php if ($account->username !== $_SESSION['username']): ?>
                                                        <a href="/lab01/account/deleteAccount/<?= $account->id ?>" 
                                                           class="btn btn-danger btn-sm" 
                                                           title="Xóa"
                                                           onclick="return confirm('Bạn có chắc chắn muốn xóa tài khoản này?')">
                                                            <i class="fas fa-trash"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> Không có tài khoản nào trong hệ thống.
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <div class="mt-3">
                <a href="/lab01/account/register" class="btn btn-success">
                    <i class="fas fa-plus"></i> Thêm tài khoản mới
                </a>
                <a href="/lab01/account/index" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Quay lại
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Font Awesome for icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

<?php include_once 'app/views/shares/footer.php'; ?>
