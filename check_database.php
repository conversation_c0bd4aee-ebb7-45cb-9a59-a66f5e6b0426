<?php
// <PERSON>ript kiểm tra cấu trúc database
try {
    require_once('app/config/database.php');
    $db = (new Database())->getConnection();
    
    echo "<h2>Kiểm tra cấu trúc bảng orders</h2>";
    
    // Kiểm tra cấu trúc bảng orders
    $stmt = $db->prepare("DESCRIBE orders");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Cấu trú<PERSON> bảng orders hiện tại:</h3>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    $hasStatus = false;
    $hasUserId = false;
    $hasOrderDate = false;
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>{$column['Field']}</td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "<td>{$column['Extra']}</td>";
        echo "</tr>";
        
        if ($column['Field'] === 'status') $hasStatus = true;
        if ($column['Field'] === 'user_id') $hasUserId = true;
        if ($column['Field'] === 'order_date') $hasOrderDate = true;
    }
    echo "</table>";
    
    echo "<h3>Kiểm tra các cột cần thiết:</h3>";
    echo "<p>Cột 'status': " . ($hasStatus ? "✓ Có" : "✗ Không có") . "</p>";
    echo "<p>Cột 'user_id': " . ($hasUserId ? "✓ Có" : "✗ Không có") . "</p>";
    echo "<p>Cột 'order_date': " . ($hasOrderDate ? "✓ Có" : "✗ Không có") . "</p>";
    
    // Nếu thiếu cột, tạo câu lệnh ALTER
    if (!$hasStatus || !$hasUserId || !$hasOrderDate) {
        echo "<h3>Câu lệnh SQL cần chạy:</h3>";
        echo "<pre>";
        
        if (!$hasUserId) {
            echo "ALTER TABLE `orders` ADD COLUMN `user_id` int NULL AFTER `id`;\n";
            echo "ALTER TABLE `orders` ADD CONSTRAINT `orders_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `account` (`id`) ON DELETE SET NULL;\n";
        }
        
        if (!$hasStatus) {
            echo "ALTER TABLE `orders` ADD COLUMN `status` enum('pending','processing','completed','cancelled') DEFAULT 'pending' AFTER `address`;\n";
        }
        
        if (!$hasOrderDate) {
            echo "ALTER TABLE `orders` CHANGE COLUMN `created_at` `order_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP;\n";
        }
        
        echo "</pre>";
        
        // Tự động chạy các câu lệnh ALTER
        echo "<h3>Tự động cập nhật database:</h3>";
        
        try {
            if (!$hasUserId) {
                $db->exec("ALTER TABLE `orders` ADD COLUMN `user_id` int NULL AFTER `id`");
                echo "<p>✓ Đã thêm cột user_id</p>";
                
                $db->exec("ALTER TABLE `orders` ADD CONSTRAINT `orders_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `account` (`id`) ON DELETE SET NULL");
                echo "<p>✓ Đã thêm foreign key cho user_id</p>";
            }
            
            if (!$hasStatus) {
                $db->exec("ALTER TABLE `orders` ADD COLUMN `status` enum('pending','processing','completed','cancelled') DEFAULT 'pending' AFTER `address`");
                echo "<p>✓ Đã thêm cột status</p>";
            }
            
            if (!$hasOrderDate) {
                // Kiểm tra xem có cột created_at không
                $hasCreatedAt = false;
                foreach ($columns as $column) {
                    if ($column['Field'] === 'created_at') {
                        $hasCreatedAt = true;
                        break;
                    }
                }
                
                if ($hasCreatedAt) {
                    $db->exec("ALTER TABLE `orders` CHANGE COLUMN `created_at` `order_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP");
                    echo "<p>✓ Đã đổi tên created_at thành order_date</p>";
                } else {
                    $db->exec("ALTER TABLE `orders` ADD COLUMN `order_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP");
                    echo "<p>✓ Đã thêm cột order_date</p>";
                }
            }
            
            echo "<p style='color: green;'><strong>✓ Database đã được cập nhật thành công!</strong></p>";
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>Lỗi khi cập nhật database: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p style='color: green;'><strong>✓ Database đã có đầy đủ các cột cần thiết!</strong></p>";
    }
    
    // Kiểm tra lại sau khi cập nhật
    echo "<h3>Cấu trúc bảng orders sau khi cập nhật:</h3>";
    $stmt = $db->prepare("DESCRIBE orders");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>{$column['Field']}</td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "<td>{$column['Extra']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Test dữ liệu mẫu
    echo "<h3>Dữ liệu trong bảng orders:</h3>";
    $stmt = $db->prepare("SELECT * FROM orders LIMIT 5");
    $stmt->execute();
    $orders = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($orders)) {
        echo "<p>Chưa có đơn hàng nào.</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr>";
        foreach (array_keys($orders[0]) as $key) {
            echo "<th>{$key}</th>";
        }
        echo "</tr>";
        
        foreach ($orders as $order) {
            echo "<tr>";
            foreach ($order as $value) {
                echo "<td>" . htmlspecialchars($value) . "</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Lỗi: " . $e->getMessage() . "</p>";
}

echo "<p><a href='/lab01/product'>Về trang chủ</a></p>";
echo "<p><a href='/lab01/account/orderManagement'>Test quản lý đơn hàng</a></p>";
?>
