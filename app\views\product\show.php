<?php include 'app/views/shares/header.php'; ?>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow-lg">
                <div class="product-image-container" style="height: 350px; overflow: hidden; background: #f8f9fa;">
                    <?php if ($product->image): ?>
                        <img src="/lab01/<?php echo htmlspecialchars($product->image, ENT_QUOTES, 'UTF-8'); ?>"
                             class="card-img-top w-100 h-100" alt="<?php echo htmlspecialchars($product->name, ENT_QUOTES, 'UTF-8'); ?>"
                             style="object-fit: cover;">
                    <?php else: ?>
                        <div class="d-flex align-items-center justify-content-center h-100 text-muted">
                            <div class="text-center">
                                <i class="fas fa-image fa-3x mb-2"></i>
                                <p>Chưa có hình ảnh</p>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
                <div class="card-body">
                    <h2 class="card-title mb-3"><?php echo htmlspecialchars($product->name, ENT_QUOTES, 'UTF-8'); ?></h2>
                    <p class="card-text mb-2">
                        <strong>Danh mục:</strong>
                        <?php echo htmlspecialchars($product->category_name ?? '', ENT_QUOTES, 'UTF-8'); ?>
                    </p>
                    <p class="card-text mb-2">
                        <strong>Mô tả:</strong>
                        <?php echo nl2br(htmlspecialchars($product->description, ENT_QUOTES, 'UTF-8')); ?>
                    </p>
                    <p class="card-text mb-4">
                        <strong>Giá:</strong>
                        <span class="fw-bold text-danger"><?php echo number_format($product->price, 0, ',', '.') . " VND"; ?></span>
                    </p>
                    <div class="d-flex flex-wrap gap-2">
                        <!-- Nút cho tất cả người dùng -->
                        <a href="/lab01/product/addToCart/<?php echo $product->id; ?>" class="btn btn-primary">
                            <i class="fas fa-shopping-cart"></i> Thêm vào giỏ hàng
                        </a>

                        <!-- Nút chỉ dành cho admin -->
                        <?php if(SessionHelper::isAdmin()): ?>
                        <a href="/lab01/product/edit/<?php echo $product->id; ?>" class="btn btn-warning">
                            <i class="fas fa-edit"></i> Sửa
                        </a>
                        <a href="/lab01/product/delete/<?php echo $product->id; ?>" class="btn btn-danger"
                           onclick="return confirm('Bạn có chắc chắn muốn xóa sản phẩm này?');">
                            <i class="fas fa-trash"></i> Xóa
                        </a>
                        <?php endif; ?>

                        <!-- Nút quay lại -->
                        <a href="/lab01/product/index" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Quay lại danh sách
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'app/views/shares/footer.php'; ?>