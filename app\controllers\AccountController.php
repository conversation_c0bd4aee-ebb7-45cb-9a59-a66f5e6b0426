<?php
require_once('app/config/database.php');
require_once('app/models/AccountModel.php');
require_once('app/models/OrderModel.php');
class AccountController {
private $accountModel;
private $orderModel;
private $db;
public function __construct() {
$this->db = (new Database())->getConnection();
$this->accountModel = new AccountModel($this->db);
$this->orderModel = new OrderModel($this->db);
}

public function index() {
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }

    // Kiểm tra đăng nhập
    if (!isset($_SESSION['username'])) {
        header('Location: /lab01/account/login');
        exit;
    }

    // Lấy thông tin tài khoản hiện tại
    $account = $this->accountModel->getAccountByUsername($_SESSION['username']);

    // Hi<PERSON>n thị thông tin cá nhân của user đang đăng nhập
    include_once 'app/views/account/profile.php';
}

// Đổi mật khẩu
public function changePassword() {
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }

    // Kiểm tra đăng nhập
    if (!isset($_SESSION['username'])) {
        header('Location: /lab01/account/login');
        exit;
    }

    $passwordErrors = [];
    $passwordSuccess = null;

    if ($_SERVER['REQUEST_METHOD'] == 'POST') {
        $currentPassword = $_POST['current_password'] ?? '';
        $newPassword = $_POST['new_password'] ?? '';
        $confirmPassword = $_POST['confirm_password'] ?? '';

        // Validation
        if (empty($currentPassword)) {
            $passwordErrors[] = "Vui lòng nhập mật khẩu hiện tại!";
        }
        if (empty($newPassword)) {
            $passwordErrors[] = "Vui lòng nhập mật khẩu mới!";
        } elseif (strlen($newPassword) < 6) {
            $passwordErrors[] = "Mật khẩu mới phải có ít nhất 6 ký tự!";
        }
        if (empty($confirmPassword)) {
            $passwordErrors[] = "Vui lòng xác nhận mật khẩu mới!";
        } elseif ($newPassword !== $confirmPassword) {
            $passwordErrors[] = "Mật khẩu mới và xác nhận không khớp!";
        }

        // Nếu không có lỗi validation, kiểm tra mật khẩu hiện tại
        if (empty($passwordErrors)) {
            $account = $this->accountModel->getAccountByUsername($_SESSION['username']);

            if (!$account) {
                $passwordErrors[] = "Không tìm thấy tài khoản!";
            } elseif (!password_verify($currentPassword, $account->password)) {
                $passwordErrors[] = "Mật khẩu hiện tại không đúng!";
            } else {
                // Mật khẩu hiện tại đúng, tiến hành đổi mật khẩu
                $hashedNewPassword = password_hash($newPassword, PASSWORD_BCRYPT, ['cost' => 12]);
                $result = $this->accountModel->updatePassword($_SESSION['username'], $hashedNewPassword);

                if ($result) {
                    $passwordSuccess = "Đổi mật khẩu thành công!";
                } else {
                    $passwordErrors[] = "Có lỗi xảy ra khi đổi mật khẩu. Vui lòng thử lại!";
                }
            }
        }
    }

    // Lấy thông tin tài khoản để hiển thị
    $account = $this->accountModel->getAccountByUsername($_SESSION['username']);

    // Hiển thị trang profile với thông báo
    include_once 'app/views/account/profile.php';
}

function register(){
include_once 'app/views/account/register.php';
}
public function login() {
include_once 'app/views/account/login.php';
}
function logout(){
unset($_SESSION['username']);
unset($_SESSION['role']);
header('Location: /lab01/product');
}
public function checkLogin(){
// Kiểm tra xem liệu form đã được submit
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
$username = $_POST['username'] ?? '';
$password = $_POST['password'] ?? '';
$account = $this->accountModel->getAccountByUsername($username);
if ($account) {
$pwd_hashed = $account->password;
//check mat khau
if (password_verify($password, $pwd_hashed)) {
session_start();
// $_SESSION['user_id'] = $account->id;
// $_SESSION['user_role'] = $account->role;
$_SESSION['username'] = $account->username;
$_SESSION['role'] = $account->role;
header('Location: /lab01/product');
exit;
}
else {
echo "Password incorrect.";
}
} else {
echo "Bao loi khong tim thay tai khoan";
}
}
}
public function forgotPassword()
{
    include_once 'app/views/account/forgotpassword.php';
}
public function saveaccount(){
    if ($_SERVER['REQUEST_METHOD'] == 'POST') {
        $username = $_POST['username'] ?? '';
        $fullName = $_POST['fullname'] ?? '';
        $email = $_POST['email'] ?? '';
        $verificationEmail = $_POST['verification_email'] ?? '';
        $phone = $_POST['phone'] ?? '';
        $password = $_POST['password'] ?? '';
        $confirmPassword = $_POST['confirmpassword'] ?? '';
        $errors =[];

        if(empty($username)){
            $errors['username'] = "Vui lòng nhập userName!";
        }
        if(empty($fullName)){
            $errors['fullname'] = "Vui lòng nhập fullName!";
        }
        if(empty($email)){
            $errors['email'] = "Vui lòng nhập email!";
        } elseif(!filter_var($email, FILTER_VALIDATE_EMAIL)){
            $errors['email'] = "Email không đúng định dạng!";
        }
        if(empty($verificationEmail)){
            $errors['verification_email'] = "Vui lòng nhập email xác thực!";
        } elseif(!filter_var($verificationEmail, FILTER_VALIDATE_EMAIL)){
            $errors['verification_email'] = "Email xác thực không đúng định dạng!";
        }
        if(empty($phone)){
            $errors['phone'] = "Vui lòng nhập số điện thoại!";
        } elseif(!preg_match('/^[0-9\-\+\s]{9,15}$/', $phone)){
            $errors['phone'] = "Số điện thoại không hợp lệ!";
        }
        if(empty($password)){
            $errors['password'] = "Vui lòng nhập password!";
        }
        if($password != $confirmPassword){
            $errors['confirmPass'] = "Mật khẩu và xác nhận chưa đúng";
        }

        // Kiểm tra username đã được đăng ký chưa?
        $account = $this->accountModel->getAccountByUsername($username);
        if($account){
            $errors['account'] = "Tài khoản này đã có người đăng ký!";
        }

        // Kiểm tra email đã được đăng ký chưa?
        $accountByEmail = $this->accountModel->getAccountByEmail($email);
        if($accountByEmail){
            $errors['email'] = "Email này đã có người đăng ký!";
        }

        if(count($errors) > 0){
            include_once 'app/views/account/register.php';
        }else{
            $password = password_hash($password, PASSWORD_BCRYPT, ['cost' => 12]);
            $result = $this->accountModel->saveaccount($username, $fullName, $email, $verificationEmail, $phone, $password);
            if($result){
                header('Location: /lab01/account/login');
            }
        }
    }
}
public function handleforgotPassword() {
    $errors = [];
    $success = null;

    if ($_SERVER['REQUEST_METHOD'] == 'POST') {
        $username = trim($_POST['username'] ?? '');
        $verificationEmail = trim($_POST['verification_email'] ?? '');

        // Validate input
        if (empty($username)) {
            $errors[] = "Vui lòng nhập tên tài khoản!";
        }
        if (empty($verificationEmail)) {
            $errors[] = "Vui lòng nhập email xác thực!";
        } elseif (!filter_var($verificationEmail, FILTER_VALIDATE_EMAIL)) {
            $errors[] = "Email xác thực không đúng định dạng!";
        }

        // Nếu không có lỗi, tiến hành xác thực
        if (count($errors) === 0) {
            $account = $this->accountModel->verifyUsernameAndEmail($username, $verificationEmail);

            if (!$account) {
                $errors[] = "Tên tài khoản hoặc email xác thực không chính xác!";
            } else {
                // Xác thực thành công, tạo mật khẩu mới
                $newPassword = bin2hex(random_bytes(4)); // 8 ký tự
                $hashed = password_hash($newPassword, PASSWORD_BCRYPT, ['cost' => 12]);
                $updateResult = $this->accountModel->updatePassword($account->username, $hashed);

                if ($updateResult) {
                    $success = "Đổi mật khẩu thành công! Mật khẩu mới của bạn là: <b>$newPassword</b>. Hãy đăng nhập lại và đổi mật khẩu!";
                } else {
                    $errors[] = "Có lỗi xảy ra khi cập nhật mật khẩu. Vui lòng thử lại!";
                }
            }
        }
    }

    // Hiển thị form và kết quả
    include_once 'app/views/account/forgotpassword.php';
}

// Quản lý đơn hàng - chỉ dành cho admin
public function orderManagement() {
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }

    // Kiểm tra quyền admin
    if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
        header('Location: /lab01/account/login');
        exit;
    }

    $orders = $this->orderModel->getAllOrders();
    include_once 'app/views/account/order_management.php';
}

// Cập nhật trạng thái đơn hàng
public function updateOrderStatus() {
    session_start();

    // Kiểm tra quyền admin
    if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
        header('Location: /lab01/account/login');
        exit;
    }

    if ($_SERVER['REQUEST_METHOD'] == 'POST') {
        $orderId = $_POST['order_id'] ?? '';
        $status = $_POST['status'] ?? '';

        if (!empty($orderId) && !empty($status)) {
            $result = $this->orderModel->updateOrderStatus($orderId, $status);
            if ($result) {
                header('Location: /lab01/account/orderManagement?success=1');
            } else {
                header('Location: /lab01/account/orderManagement?error=1');
            }
        } else {
            header('Location: /lab01/account/orderManagement?error=1');
        }
    }
}

// Xem chi tiết đơn hàng
public function orderDetail($orderId) {
    session_start();

    // Kiểm tra quyền admin
    if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
        header('Location: /lab01/account/login');
        exit;
    }

    $order = $this->orderModel->getOrderByIdWithDetails($orderId);
    $orderDetails = $this->orderModel->getOrderDetails($orderId);

    if ($order) {
        include_once 'app/views/account/order_detail.php';
    } else {
        echo "Không tìm thấy đơn hàng.";
    }
}

// Quản lý tài khoản - chỉ dành cho admin
public function accountManagement() {
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }

    // Kiểm tra quyền admin
    if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
        header('Location: /lab01/account/login');
        exit;
    }

    $accounts = $this->accountModel->getAllAccounts();
    include_once 'app/views/account/account_management.php';
}

// Xem chi tiết tài khoản
public function accountDetail($accountId) {
    session_start();

    // Kiểm tra quyền admin
    if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
        header('Location: /lab01/account/login');
        exit;
    }

    $account = $this->accountModel->getAccountById($accountId);
    if ($account) {
        include_once 'app/views/account/account_detail.php';
    } else {
        echo "Không tìm thấy tài khoản.";
    }
}

// Chỉnh sửa tài khoản
public function editAccount($accountId) {
    session_start();

    // Kiểm tra quyền admin
    if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
        header('Location: /lab01/account/login');
        exit;
    }

    $account = $this->accountModel->getAccountById($accountId);
    $errors = [];

    if ($account) {
        include_once 'app/views/account/edit_account.php';
    } else {
        echo "Không tìm thấy tài khoản.";
    }
}

// Cập nhật tài khoản
public function updateAccount($accountId) {
    session_start();

    // Kiểm tra quyền admin
    if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
        header('Location: /lab01/account/login');
        exit;
    }

    if ($_SERVER['REQUEST_METHOD'] == 'POST') {
        $username = $_POST['username'] ?? '';
        $fullname = $_POST['fullname'] ?? '';
        $email = $_POST['email'] ?? '';
        $verificationEmail = $_POST['verification_email'] ?? '';
        $phone = $_POST['phone'] ?? '';
        $role = $_POST['role'] ?? '';
        $errors = [];

        // Validation
        if (empty($username)) {
            $errors['username'] = "Vui lòng nhập tên đăng nhập!";
        }
        if (empty($fullname)) {
            $errors['fullname'] = "Vui lòng nhập họ tên!";
        }
        if (empty($email)) {
            $errors['email'] = "Vui lòng nhập email!";
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $errors['email'] = "Email không đúng định dạng!";
        }
        if (empty($verificationEmail)) {
            $errors['verification_email'] = "Vui lòng nhập email xác thực!";
        } elseif (!filter_var($verificationEmail, FILTER_VALIDATE_EMAIL)) {
            $errors['verification_email'] = "Email xác thực không đúng định dạng!";
        }

        // Kiểm tra username trùng (trừ chính tài khoản đang sửa)
        $existingAccount = $this->accountModel->getAccountByUsername($username);
        if ($existingAccount && $existingAccount->id != $accountId) {
            $errors['username'] = "Tên đăng nhập đã tồn tại!";
        }

        // Kiểm tra email trùng (trừ chính tài khoản đang sửa)
        $existingEmailAccount = $this->accountModel->getAccountByEmail($email);
        if ($existingEmailAccount && $existingEmailAccount->id != $accountId) {
            $errors['email'] = "Email đã tồn tại!";
        }

        if (count($errors) === 0) {
            $result = $this->accountModel->updateAccount($accountId, $username, $fullname, $email, $verificationEmail, $phone, $role);
            if ($result) {
                header('Location: /lab01/account/accountManagement?success=1');
                exit;
            } else {
                $errors['general'] = "Có lỗi xảy ra khi cập nhật tài khoản!";
            }
        }

        // Nếu có lỗi, hiển thị lại form
        $account = $this->accountModel->getAccountById($accountId);
        include_once 'app/views/account/edit_account.php';
    }
}

// Xóa tài khoản
public function deleteAccount($accountId) {
    session_start();

    // Kiểm tra quyền admin
    if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
        header('Location: /lab01/account/login');
        exit;
    }

    // Không cho phép xóa chính mình
    $currentAccount = $this->accountModel->getAccountByUsername($_SESSION['username']);
    if ($currentAccount && $currentAccount->id == $accountId) {
        header('Location: /lab01/account/accountManagement?error=self_delete');
        exit;
    }

    $result = $this->accountModel->deleteAccount($accountId);
    if ($result) {
        header('Location: /lab01/account/accountManagement?success=delete');
    } else {
        header('Location: /lab01/account/accountManagement?error=delete');
    }
    exit;
}

// Thêm đơn hàng mới
public function addOrder() {
    SessionHelper::requireAdmin();

    $errors = [];
    $accounts = $this->accountModel->getAllAccounts();

    if ($_SERVER['REQUEST_METHOD'] == 'POST') {
        $user_id = $_POST['user_id'] ?? null;
        $name = $_POST['name'] ?? '';
        $phone = $_POST['phone'] ?? '';
        $address = $_POST['address'] ?? '';
        $status = $_POST['status'] ?? 'pending';

        $result = $this->orderModel->addOrder($user_id, $name, $phone, $address, $status);

        if (is_array($result)) {
            $errors = $result;
        } else {
            header('Location: /lab01/account/orderManagement?success=add');
            exit;
        }
    }

    include_once 'app/views/account/add_order.php';
}

// Sửa đơn hàng
public function editOrder($orderId) {
    SessionHelper::requireAdmin();

    $order = $this->orderModel->getOrderByIdWithDetails($orderId);
    $accounts = $this->accountModel->getAllAccounts();
    $errors = [];

    if (!$order) {
        header('Location: /lab01/account/orderManagement?error=not_found');
        exit;
    }

    if ($_SERVER['REQUEST_METHOD'] == 'POST') {
        $user_id = $_POST['user_id'] ?? null;
        $name = $_POST['name'] ?? '';
        $phone = $_POST['phone'] ?? '';
        $address = $_POST['address'] ?? '';
        $status = $_POST['status'] ?? 'pending';

        $result = $this->orderModel->updateOrder($orderId, $user_id, $name, $phone, $address, $status);

        if (is_array($result)) {
            $errors = $result;
        } elseif ($result) {
            header('Location: /lab01/account/orderManagement?success=edit');
            exit;
        } else {
            $errors['general'] = 'Có lỗi xảy ra khi cập nhật đơn hàng';
        }
    }

    include_once 'app/views/account/edit_order.php';
}

// Xóa đơn hàng
public function deleteOrder() {
    SessionHelper::requireAdmin();

    if ($_SERVER['REQUEST_METHOD'] == 'POST') {
        $orderId = $_POST['order_id'] ?? null;

        if ($orderId) {
            $result = $this->orderModel->deleteOrder($orderId);
            if ($result) {
                header('Location: /lab01/account/orderManagement?success=delete');
            } else {
                header('Location: /lab01/account/orderManagement?error=delete');
            }
        } else {
            header('Location: /lab01/account/orderManagement?error=not_found');
        }
    } else {
        header('Location: /lab01/account/orderManagement');
    }
    exit;
}
}