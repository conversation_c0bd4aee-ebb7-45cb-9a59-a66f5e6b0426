<?php include 'app/views/shares/header.php'; ?>
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <h2><PERSON><PERSON><PERSON> thực qua đơn hàng đã mua</h2>
            
            <?php
            if (isset($errors) && count($errors) > 0) {
                echo "<div class='alert alert-danger'><ul>";
                foreach ($errors as $err) {
                    echo "<li>$err</li>";
                }
                echo "</ul></div>";
            }
            ?>
            
            <div class="alert alert-info">
                <strong>Tài khoản:</strong> <?php echo htmlspecialchars($account->username); ?><br>
                <strong>Họ tên:</strong> <?php echo htmlspecialchars($account->fullname); ?>
            </div>
            
            <p>Vui lòng chọn một trong các đơn hàng mà bạn đã mua để xá<PERSON> thực:</p>
            
            <form action="/lab01/account/handleforgotpassword" method="post">
                <input type="hidden" name="username" value="<?php echo htmlspecialchars($account->username); ?>">
                
                <div class="row">
                    <?php foreach($orders as $order): ?>
                    <div class="col-md-6 mb-3">
                        <div class="card">
                            <div class="card-body">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="order_id" id="order_<?php echo $order->id; ?>" value="<?php echo $order->id; ?>" required>
                                    <label class="form-check-label" for="order_<?php echo $order->id; ?>">
                                        <strong>Mã đơn hàng: #<?php echo $order->id; ?></strong><br>
                                        <small class="text-muted">
                                            Ngày đặt: <?php echo date('d/m/Y H:i', strtotime($order->order_date)); ?><br>
                                            Người nhận: <?php echo htmlspecialchars($order->name); ?><br>
                                            Địa chỉ: <?php echo htmlspecialchars($order->address); ?><br>
                                            Tổng tiền: <?php echo number_format($order->total, 0, ',', '.'); ?> VNĐ<br>
                                            Trạng thái: 
                                            <?php 
                                            $statusLabels = [
                                                'pending' => 'Chờ xử lý',
                                                'processing' => 'Đang xử lý',
                                                'shipped' => 'Đã gửi hàng',
                                                'delivered' => 'Đã giao hàng',
                                                'cancelled' => 'Đã hủy'
                                            ];
                                            echo $statusLabels[$order->status] ?? $order->status;
                                            ?>
                                        </small>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                
                <div class="mt-4">
                    <button type="submit" class="btn btn-primary">Xác thực</button>
                    <a href="/lab01/account/forgotpassword" class="btn btn-secondary">Quay lại</a>
                </div>
            </form>
        </div>
    </div>
</div>
<?php include 'app/views/shares/footer.php'; ?>
