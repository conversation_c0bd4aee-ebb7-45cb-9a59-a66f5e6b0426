<?php
class SessionHelper {
    public static function isLoggedIn() {
        return isset($_SESSION['username']);
    }

    public static function isAdmin() {
        return isset($_SESSION['username']) && isset($_SESSION['role']) && $_SESSION['role'] === 'admin';
    }

    public static function isUser() {
        return isset($_SESSION['username']) && isset($_SESSION['role']) && $_SESSION['role'] === 'user';
    }

    public static function requireLogin() {
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }

        if (!self::isLoggedIn()) {
            header('Location: /lab01/account/login');
            exit;
        }
    }

    public static function requireAdmin() {
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }

        if (!self::isLoggedIn()) {
            header('Location: /lab01/account/login');
            exit;
        }

        if (!self::isAdmin()) {
            include_once 'app/views/errors/access_denied.php';
            exit;
        }
    }

    public static function getUserRole() {
        return $_SESSION['role'] ?? null;
    }

    public static function getUsername() {
        return $_SESSION['username'] ?? null;
    }
}