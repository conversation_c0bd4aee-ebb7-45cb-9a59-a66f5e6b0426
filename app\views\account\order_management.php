<?php include 'app/views/shares/header.php'; ?>
<div class="container py-5">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">
            <i class="fas fa-clipboard-list"></i> Quản lý đơn hàng
        </h2>
        <a href="/lab01/account/addOrder" class="btn btn-success">
            <i class="fas fa-plus"></i> Thêm đơn hàng mới
        </a>
    </div>

    <?php
    if (isset($_GET['success'])) {
        $message = '';
        switch($_GET['success']) {
            case 'update': $message = 'Cập nhật trạng thái đơn hàng thành công!'; break;
            case 'add': $message = 'Thêm đơn hàng thành công!'; break;
            case 'edit': $message = 'Cập nhật đơn hàng thành công!'; break;
            case 'delete': $message = 'Xóa đơn hàng thành công!'; break;
            default: $message = 'Thao tác thành công!';
        }
        echo "<div class='alert alert-success alert-dismissible fade show' role='alert'>
                <i class='fas fa-check-circle'></i> {$message}
                <button type='button' class='close' data-dismiss='alert'>
                    <span>&times;</span>
                </button>
              </div>";
    }
    if (isset($_GET['error'])) {
        $message = '';
        switch($_GET['error']) {
            case 'update': $message = 'Có lỗi xảy ra khi cập nhật trạng thái đơn hàng!'; break;
            case 'add': $message = 'Có lỗi xảy ra khi thêm đơn hàng!'; break;
            case 'edit': $message = 'Có lỗi xảy ra khi cập nhật đơn hàng!'; break;
            case 'delete': $message = 'Có lỗi xảy ra khi xóa đơn hàng!'; break;
            case 'not_found': $message = 'Không tìm thấy đơn hàng!'; break;
            default: $message = 'Có lỗi xảy ra!';
        }
        echo "<div class='alert alert-danger alert-dismissible fade show' role='alert'>
                <i class='fas fa-exclamation-triangle'></i> {$message}
                <button type='button' class='close' data-dismiss='alert'>
                    <span>&times;</span>
                </button>
              </div>";
    }
    ?>
    
    <div class="table-responsive">
        <table class="table table-bordered table-hover">
            <thead class="thead-dark">
                <tr>
                    <th>Mã đơn hàng</th>
                    <th>Tài khoản</th>
                    <th>Người nhận</th>
                    <th>Ngày đặt</th>
                    <th>Tổng tiền</th>
                    <th>Trạng thái</th>
                    <th>Hành động</th>
                </tr>
            </thead>
            <tbody>
                <?php if (!empty($orders)) : ?>
                    <?php foreach ($orders as $order) : ?>
                        <tr>
                            <td>#<?= htmlspecialchars($order->id) ?></td>
                            <td>
                                <?php if ($order->username): ?>
                                    <strong><?= htmlspecialchars($order->username) ?></strong><br>
                                    <small><?= htmlspecialchars($order->fullname) ?></small>
                                <?php else: ?>
                                    <em>Khách vãng lai</em>
                                <?php endif; ?>
                            </td>
                            <td>
                                <strong><?= htmlspecialchars($order->name) ?></strong><br>
                                <small><?= htmlspecialchars($order->phone) ?></small>
                            </td>
                            <td><?= date('d/m/Y H:i', strtotime($order->order_date)) ?></td>
                            <td><?= number_format($order->total, 0, ',', '.') ?> VNĐ</td>
                            <td>
                                <?php 
                                $statusLabels = [
                                    'pending' => ['label' => 'Chờ xử lý', 'class' => 'warning'],
                                    'processing' => ['label' => 'Đang xử lý', 'class' => 'info'],
                                    'shipped' => ['label' => 'Đã gửi hàng', 'class' => 'primary'],
                                    'delivered' => ['label' => 'Đã giao hàng', 'class' => 'success'],
                                    'cancelled' => ['label' => 'Đã hủy', 'class' => 'danger']
                                ];
                                $status = $statusLabels[$order->status] ?? ['label' => $order->status, 'class' => 'secondary'];
                                ?>
                                <span class="badge badge-<?= $status['class'] ?>"><?= $status['label'] ?></span>
                            </td>
                            <td>
                                <div class="btn-group-vertical btn-group-sm" role="group">
                                    <!-- Hàng đầu: Chi tiết và Sửa -->
                                    <div class="btn-group mb-1" role="group">
                                        <a href="/lab01/account/orderDetail/<?= $order->id ?>" class="btn btn-info btn-sm">
                                            <i class="fas fa-eye"></i> Chi tiết
                                        </a>
                                        <a href="/lab01/account/editOrder/<?= $order->id ?>" class="btn btn-warning btn-sm">
                                            <i class="fas fa-edit"></i> Sửa
                                        </a>
                                    </div>

                                    <!-- Hàng thứ hai: Trạng thái và Xóa -->
                                    

                                        <!-- Nút xóa -->
                                        <form method="POST" action="/lab01/account/deleteOrder" style="display: inline;">
                                            <input type="hidden" name="order_id" value="<?= $order->id ?>">
                                            <button type="submit" class="btn btn-danger btn-sm"
                                                    onclick="return confirm('Bạn có chắc chắn muốn xóa đơn hàng này? Hành động này không thể hoàn tác!')">
                                                <i class="fas fa-trash"></i> Xóa
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php else : ?>
                    <tr>
                        <td colspan="7" class="text-center">Không có đơn hàng nào.</td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

<!-- Custom CSS -->
<style>
.btn-group-vertical .btn-group {
    width: 100%;
}
.btn-group-vertical .btn-group .btn {
    border-radius: 0.25rem;
}
.table td {
    vertical-align: middle;
}
.badge {
    font-size: 0.8em;
}
</style>

<!-- Bootstrap JS cho dropdown -->
<script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/dist/js/bootstrap.bundle.min.js"></script>

<?php include 'app/views/shares/footer.php'; ?>
