<?php include 'app/views/shares/header.php'; ?>
<div class="container py-5">
    <h2 class="mb-4">Quản lý đơn hàng</h2>
    
    <?php
    if (isset($_GET['success'])) {
        echo "<div class='alert alert-success'>Cập nhật trạng thái đơn hàng thành công!</div>";
    }
    if (isset($_GET['error'])) {
        echo "<div class='alert alert-danger'>Có lỗi xảy ra khi cập nhật trạng thái đơn hàng!</div>";
    }
    ?>
    
    <div class="table-responsive">
        <table class="table table-bordered table-hover">
            <thead class="thead-dark">
                <tr>
                    <th>Mã đơn hàng</th>
                    <th>Tài khoản</th>
                    <th>Người nhận</th>
                    <th>Ngày đặt</th>
                    <th>Tổng tiền</th>
                    <th>Tr<PERSON><PERSON> thái</th>
                    <th>Hành động</th>
                </tr>
            </thead>
            <tbody>
                <?php if (!empty($orders)) : ?>
                    <?php foreach ($orders as $order) : ?>
                        <tr>
                            <td>#<?= htmlspecialchars($order->id) ?></td>
                            <td>
                                <?php if ($order->username): ?>
                                    <strong><?= htmlspecialchars($order->username) ?></strong><br>
                                    <small><?= htmlspecialchars($order->fullname) ?></small>
                                <?php else: ?>
                                    <em>Khách vãng lai</em>
                                <?php endif; ?>
                            </td>
                            <td>
                                <strong><?= htmlspecialchars($order->name) ?></strong><br>
                                <small><?= htmlspecialchars($order->phone) ?></small>
                            </td>
                            <td><?= date('d/m/Y H:i', strtotime($order->order_date)) ?></td>
                            <td><?= number_format($order->total, 0, ',', '.') ?> VNĐ</td>
                            <td>
                                <?php 
                                $statusLabels = [
                                    'pending' => ['label' => 'Chờ xử lý', 'class' => 'warning'],
                                    'processing' => ['label' => 'Đang xử lý', 'class' => 'info'],
                                    'shipped' => ['label' => 'Đã gửi hàng', 'class' => 'primary'],
                                    'delivered' => ['label' => 'Đã giao hàng', 'class' => 'success'],
                                    'cancelled' => ['label' => 'Đã hủy', 'class' => 'danger']
                                ];
                                $status = $statusLabels[$order->status] ?? ['label' => $order->status, 'class' => 'secondary'];
                                ?>
                                <span class="badge badge-<?= $status['class'] ?>"><?= $status['label'] ?></span>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="/lab01/account/orderDetail/<?= $order->id ?>" class="btn btn-info btn-sm">Chi tiết</a>
                                    
                                    <!-- Dropdown để thay đổi trạng thái -->
                                    <div class="dropdown">
                                        <button class="btn btn-secondary btn-sm dropdown-toggle" type="button" data-toggle="dropdown">
                                            Trạng thái
                                        </button>
                                        <div class="dropdown-menu">
                                            <?php foreach($statusLabels as $statusKey => $statusInfo): ?>
                                                <?php if ($statusKey !== $order->status): ?>
                                                    <form method="POST" action="/lab01/account/updateOrderStatus" style="display: inline;">
                                                        <input type="hidden" name="order_id" value="<?= $order->id ?>">
                                                        <input type="hidden" name="status" value="<?= $statusKey ?>">
                                                        <button type="submit" class="dropdown-item" 
                                                                onclick="return confirm('Bạn có chắc chắn muốn thay đổi trạng thái đơn hàng này?')">
                                                            <?= $statusInfo['label'] ?>
                                                        </button>
                                                    </form>
                                                <?php endif; ?>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php else : ?>
                    <tr>
                        <td colspan="7" class="text-center">Không có đơn hàng nào.</td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

<!-- Bootstrap JS cho dropdown -->
<script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/dist/js/bootstrap.bundle.min.js"></script>

<?php include 'app/views/shares/footer.php'; ?>
