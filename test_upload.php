<?php
// Test script để kiểm tra upload hình ảnh
session_start();

// <PERSON><PERSON><PERSON> lập đăng nhập admin
$_SESSION['username'] = 'q';
$_SESSION['role'] = 'admin';

echo "<h2>Test Upload Image</h2>";

// Ki<PERSON><PERSON> tra thư mục uploads
$uploadDir = "uploads/";
if (!is_dir($uploadDir)) {
    echo "<p>Tạo thư mục uploads...</p>";
    mkdir($uploadDir, 0777, true);
}

echo "<p>Thư mục uploads: " . (is_dir($uploadDir) ? "✓ Tồn tại" : "✗ Không tồn tại") . "</p>";
echo "<p>Quyền ghi: " . (is_writable($uploadDir) ? "✓ Có thể ghi" : "✗ Không thể ghi") . "</p>";

// Test form upload
?>
<form action="test_upload.php" method="POST" enctype="multipart/form-data">
    <div>
        <label>Chọn hình ảnh:</label>
        <input type="file" name="test_image" accept="image/*">
    </div>
    <div>
        <button type="submit" name="test_upload">Test Upload</button>
    </div>
</form>

<?php
if (isset($_POST['test_upload']) && isset($_FILES['test_image'])) {
    echo "<h3>Kết quả test upload:</h3>";
    
    $file = $_FILES['test_image'];
    echo "<pre>";
    print_r($file);
    echo "</pre>";
    
    if ($file['error'] == 0) {
        $target_file = $uploadDir . basename($file["name"]);
        
        if (move_uploaded_file($file["tmp_name"], $target_file)) {
            echo "<p>✓ Upload thành công: " . $target_file . "</p>";
            echo "<img src='" . $target_file . "' style='max-width: 200px;'>";
        } else {
            echo "<p>✗ Upload thất bại</p>";
        }
    } else {
        echo "<p>✗ Lỗi file: " . $file['error'] . "</p>";
    }
}

// Test database connection
echo "<h3>Test Database Connection:</h3>";
try {
    require_once('app/config/database.php');
    $db = (new Database())->getConnection();
    echo "<p>✓ Kết nối database thành công</p>";
    
    // Test query products
    $stmt = $db->prepare("SELECT COUNT(*) as count FROM product");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_OBJ);
    echo "<p>Số sản phẩm trong database: " . $result->count . "</p>";
    
} catch (Exception $e) {
    echo "<p>✗ Lỗi database: " . $e->getMessage() . "</p>";
}

echo "<p><a href='/lab01/product/add'>Đi đến trang thêm sản phẩm</a></p>";
echo "<p><a href='/lab01/product'>Đi đến danh sách sản phẩm</a></p>";
?>
