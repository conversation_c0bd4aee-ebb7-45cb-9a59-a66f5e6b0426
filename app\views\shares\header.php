<?php
require_once('app/helpers/SessionHelper.php');
?>
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Qu<PERSON>n lý sản phẩm</title>
<link
href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css"
rel="stylesheet">
<style>
.product-image {
max-width: 100px;
height: auto;
}
</style>

</head>
<body>
<nav class="navbar navbar-expand-lg navbar-light bg-light">
<a class="navbar-brand" href="#">Quản lý sản phẩm</a>

<button class="navbar-toggler" type="button" data-toggle="collapse" data-
target="#navbarNav"

aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle

navigation">

<span class="navbar-toggler-icon"></span>
</button>
<div class="collapse navbar-collapse" id="navbarNav">
<ul class="navbar-nav">
<li class="nav-item">
<a class="nav-link" href="/lab01/product/">Danh sách sản phẩm</a>

</li>
<?php if(SessionHelper::isAdmin()): ?>
<li class="nav-item">
<a class="nav-link" href="/lab01/product/add">Thêm sản phẩm</a>
</li>
<li class="nav-item">
        <a class="nav-link" href="/lab01/category/add">Thêm danh mục</a>
      </li>
<?php endif; ?>
      <li class="nav-item">
        <a class="nav-link" href="/lab01/category/list">Danh sách danh mục</a>
      </li>
      <li class="nav-item">
        <a class="nav-link" href="/lab01/product/cart">
          <i class="fas fa-shopping-cart"></i> Giỏ hàng
        </a>
      </li>
      <li class="nav-item">
        <a class="nav-link" href="/lab01/demo/roleDemo">
          <i class="fas fa-info-circle"></i> Demo Phân quyền
        </a>
      </li>

      <?php if(SessionHelper::isAdmin()): ?>
      <li class="nav-item">
        <a class="nav-link" href="/lab01/account/orderManagement">
          <i class="fas fa-clipboard-list"></i> Quản lý đơn hàng
        </a>
      </li>
      <li class="nav-item">
        <a class="nav-link" href="/lab01/account/accountManagement">
          <i class="fas fa-users"></i> Quản lý tài khoản
        </a>
      </li>
      <?php endif; ?>

<li class="nav-item">
<?php

if(SessionHelper::isLoggedIn()){

  echo "<a class='nav-link' href='/lab01/account/index'>".$_SESSION['username']."</a>";

}
else{
echo "<a class='nav-link'

href='/lab01/account/login'>Login</a>";
}
?>

</li>
<li class="nav-item">
</a>
<?php

if(SessionHelper::isLoggedIn()){
echo "<a class='nav-link'

href='/lab01/account/logout'>Logout</a>";
}
?>

</li>
</ul>
</div>
</nav>
<div class="container mt-4">