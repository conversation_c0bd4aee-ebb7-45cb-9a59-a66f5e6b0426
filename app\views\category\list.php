<?php include 'app/views/shares/header.php'; ?>
<div class="container py-5">
    <h1 class="mb-4 text-primary">Danh sách danh mục</h1>
    <?php if(SessionHelper::isAdmin()): ?>
    <a href="/lab01/category/add" class="btn btn-success mb-3">Thêm danh mục</a>
    <?php endif; ?>

    <?php if (empty($categories)): ?>
        <div class="alert alert-info">Chưa có danh mục nào.</div>
    <?php else: ?>
        <table class="table table-bordered table-striped">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Tên danh mục</th>
                    <th>Mô tả</th>
                    <th>Hành động</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($categories as $category): ?>
                    <tr>
                        <td><?php echo $category->id; ?></td>
                        <td><?php echo htmlspecialchars($category->name, ENT_QUOTES, 'UTF-8'); ?></td>
                        <td><?php echo htmlspecialchars($category->description, ENT_QUOTES, 'UTF-8'); ?></td>
                        <td>
                            <?php if(SessionHelper::isAdmin()): ?>
                            <a href="/lab01/category/edit/<?php echo $category->id; ?>" class="btn btn-warning btn-sm">Sửa</a>
                            <a href="/lab01/category/delete/<?php echo $category->id; ?>" onclick="return confirm('Bạn có chắc muốn xóa danh mục này?');" class="btn btn-danger btn-sm">Xóa</a>
                            <?php else: ?>
                            <span class="text-muted">Chỉ xem</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    <?php endif; ?>
</div>
<?php include 'app/views/shares/footer.php'; ?>
